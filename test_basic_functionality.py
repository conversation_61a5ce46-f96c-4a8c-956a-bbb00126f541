#!/usr/bin/env python3
"""
Basic functionality test for the Solana Trading Bot Python conversion
Tests core modules without requiring external dependencies
"""

import sys
import os
import asyncio
from pathlib import Path

# Add current directory to path
sys.path.insert(0, '.')

def test_package_structure():
    """Test that all package files exist"""
    print("🔍 Testing package structure...")
    
    required_files = [
        "trading_bot/__init__.py",
        "trading_bot/config/__init__.py",
        "trading_bot/config/settings.py",
        "trading_bot/utils/__init__.py",
        "trading_bot/utils/logging.py",
        "trading_bot/ui/__init__.py",
        "trading_bot/ui/terminal.py",
        "trading_bot/main.py",
        "trading_bot/sell.py",
        "trading_bot/test_websocket.py",
        "trading_bot/gmgn_scraper.py",
        "requirements.txt",
        "setup.py",
        "pyproject.toml",
        ".env.example"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    else:
        print(f"✅ All {len(required_files)} required files exist")
        return True

def test_configuration():
    """Test configuration system"""
    print("\n🔧 Testing configuration system...")
    
    try:
        from trading_bot.config.settings import trading_config, gmgn_config, logging_config, websocket_config
        
        print("✅ Configuration modules imported successfully")
        
        # Test trading config
        assert trading_config.minimum_buy_amount > 0, "Min buy amount should be positive"
        assert trading_config.max_concurrent_trades > 0, "Max concurrent trades should be positive"
        assert 0 <= trading_config.buy_threshold_min <= 100, "Buy threshold min should be 0-100%"
        assert 0 <= trading_config.buy_threshold_max <= 100, "Buy threshold max should be 0-100%"
        print(f"✅ Trading config validated (min buy: {trading_config.minimum_buy_amount} SOL)")
        
        # Test GMGN config
        assert gmgn_config.num_tabs > 0, "Number of tabs should be positive"
        assert gmgn_config.scrape_interval > 0, "Scrape interval should be positive"
        print(f"✅ GMGN config validated ({gmgn_config.num_tabs} tabs, {gmgn_config.scrape_interval}ms interval)")
        
        # Test logging config
        assert isinstance(logging_config.enable_logging, bool), "Enable logging should be boolean"
        assert logging_config.log_directory, "Log directory should be specified"
        print(f"✅ Logging config validated (enabled: {logging_config.enable_logging})")
        
        # Test websocket config
        assert websocket_config.url.startswith('wss://'), "WebSocket URL should use wss://"
        print(f"✅ WebSocket config validated ({websocket_config.url})")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_logging():
    """Test logging system"""
    print("\n📝 Testing logging system...")
    
    try:
        from trading_bot.utils.logging import setup_logging, get_logger, TradingBotLogger
        
        # Setup logging without file output for testing
        setup_logging(log_level='INFO', enable_file_logging=False)
        print("✅ Logging setup completed")
        
        # Test basic logger
        logger = get_logger('test')
        logger.info('Test message from basic logger')
        print("✅ Basic logger working")
        
        # Test trading bot logger
        trading_logger = TradingBotLogger('test_trading')
        trading_logger.info('Test info message')
        trading_logger.success('Test success message')
        print("✅ Trading bot logger working")
        
        return True
        
    except Exception as e:
        print(f"❌ Logging test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui():
    """Test terminal UI"""
    print("\n🖥️ Testing terminal UI...")
    
    try:
        from trading_bot.ui.terminal import TradingBotUI
        
        # Create UI instance
        ui = TradingBotUI()
        print("✅ UI instance created")
        
        # Test updating stats
        test_stats = {
            'successful_trades': 5,
            'failed_trades': 1,
            'total_tokens_analyzed': 100,
            'tokens_under_threshold': 10,
            'active_trades': 2,
            'balance': 1.5,
            'spl_tokens': [
                {'mint': 'test123...', 'amount': 100.5},
                {'mint': 'test456...', 'amount': 50.2}
            ]
        }
        ui.update_stats(test_stats)
        print("✅ Stats update working")
        
        # Test log messages
        ui.add_log_message('Test log message')
        ui.add_log_message('✅ Test success message')
        ui.add_log_message('❌ Test error message')
        ui.add_log_message('🎯 Test trade message')
        
        assert len(ui.log_messages) == 4, "Should have 4 log messages"
        print("✅ Log message system working")
        
        # Test visual modes
        ui.set_visual_mode('trading')
        assert ui.visual_mode == 'trading', "Visual mode should be 'trading'"
        ui.set_visual_mode('searching')
        assert ui.visual_mode == 'searching', "Visual mode should be 'searching'"
        print("✅ Visual mode switching working")
        
        return True
        
    except Exception as e:
        print(f"❌ UI test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_websocket_module():
    """Test WebSocket module structure"""
    print("\n🌐 Testing WebSocket module...")
    
    try:
        from trading_bot.test_websocket import PumpPortalWebSocketTest
        
        # Create test instance
        test = PumpPortalWebSocketTest()
        print("✅ WebSocket test instance created")
        
        # Test basic properties
        assert test.test_token, "Test token should be set"
        assert test.ws_url.startswith('wss://'), "WebSocket URL should use wss://"
        assert test.messages_received == 0, "Initial message count should be 0"
        print(f"✅ WebSocket test configured (token: {test.test_token[:12]}...)")
        
        return True
        
    except Exception as e:
        print(f"❌ WebSocket test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_async_functionality():
    """Test async functionality"""
    print("\n⚡ Testing async functionality...")
    
    try:
        # Test basic async operations
        async def test_async_function():
            await asyncio.sleep(0.1)
            return "async test completed"
        
        result = await test_async_function()
        assert result == "async test completed", "Async function should return expected result"
        print("✅ Basic async functionality working")
        
        # Test multiple async tasks
        async def test_task(task_id):
            await asyncio.sleep(0.05)
            return f"task_{task_id}_completed"
        
        tasks = [test_task(i) for i in range(3)]
        results = await asyncio.gather(*tasks)
        
        assert len(results) == 3, "Should have 3 task results"
        assert all("completed" in result for result in results), "All tasks should complete"
        print("✅ Multiple async tasks working")
        
        return True
        
    except Exception as e:
        print(f"❌ Async test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_structure():
    """Test that setup files are properly configured"""
    print("\n📦 Testing setup files...")
    
    try:
        # Test requirements.txt
        with open('requirements.txt', 'r') as f:
            requirements = f.read()
            assert 'pydantic' in requirements, "Requirements should include pydantic"
            assert 'rich' in requirements, "Requirements should include rich"
            assert 'loguru' in requirements, "Requirements should include loguru"
            print("✅ requirements.txt is properly configured")
        
        # Test .env.example
        with open('.env.example', 'r') as f:
            env_example = f.read()
            assert 'SOLANA_WALLET_PATH' in env_example, "Should include wallet path config"
            assert 'HELIUS_RPC_URL' in env_example, "Should include RPC URL config"
            assert 'GMGN_NUM_TABS' in env_example, "Should include GMGN config"
            print("✅ .env.example is properly configured")
        
        # Test setup.py
        with open('setup.py', 'r') as f:
            setup_content = f.read()
            assert 'solana-trading-bot-python' in setup_content, "Should include package name"
            assert 'console_scripts' in setup_content, "Should include console scripts"
            print("✅ setup.py is properly configured")
        
        return True
        
    except Exception as e:
        print(f"❌ File structure test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 SOLANA TRADING BOT - PYTHON CONVERSION TESTS")
    print("=" * 60)
    
    tests = [
        ("Package Structure", test_package_structure),
        ("Configuration System", test_configuration),
        ("Logging System", test_logging),
        ("Terminal UI", test_ui),
        ("WebSocket Module", test_websocket_module),
        ("File Structure", test_file_structure),
    ]
    
    passed = 0
    failed = 0
    
    # Run synchronous tests
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                failed += 1
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name}: FAILED - {e}")
    
    # Run async test
    try:
        result = asyncio.run(test_async_functionality())
        if result:
            passed += 1
            print("✅ Async Functionality: PASSED")
        else:
            failed += 1
            print("❌ Async Functionality: FAILED")
    except Exception as e:
        failed += 1
        print(f"❌ Async Functionality: FAILED - {e}")
    
    # Summary
    total = passed + failed
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {failed}/{total}")
    print(f"📈 Success Rate: {(passed/total)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED! The Python conversion is working correctly.")
        print("\n📋 Next steps:")
        print("1. Install full dependencies: pip install -r requirements.txt")
        print("2. Install Playwright browsers: playwright install")
        print("3. Configure .env file with your settings")
        print("4. Run the trading bot: python -m trading_bot.main")
    else:
        print(f"\n⚠️ {failed} tests failed. Please check the errors above.")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
