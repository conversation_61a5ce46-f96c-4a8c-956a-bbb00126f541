#!/usr/bin/env python3
"""
Setup script for Solana Trading Bot Python conversion
"""

from setuptools import setup, find_packages
import os

# Read the requirements file
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    with open(requirements_path, 'r') as f:
        requirements = []
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                # Remove version constraints for setup.py
                package = line.split('>=')[0].split('==')[0].split('<')[0]
                requirements.append(package)
        return requirements

setup(
    name="solana-trading-bot-python",
    version="1.0.0",
    description="A secure Solana trading bot for pump.fun tokens - Python version",
    long_description=open("README.md").read() if os.path.exists("README.md") else "",
    long_description_content_type="text/markdown",
    author="Secure Trading Bot",
    license="MIT",
    packages=find_packages(),
    install_requires=read_requirements(),
    python_requires=">=3.8",
    entry_points={
        'console_scripts': [
            'solana-trading-bot=trading_bot.main:main',
            'solana-sell-all=trading_bot.sell:main',
            'test-websocket=trading_bot.test_websocket:main',
        ],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Office/Business :: Financial :: Investment",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    keywords="solana trading bot cryptocurrency pump.fun blockchain",
    project_urls={
        "Source": "https://github.com/your-repo/solana-trading-bot-python",
        "Bug Reports": "https://github.com/your-repo/solana-trading-bot-python/issues",
    },
)
