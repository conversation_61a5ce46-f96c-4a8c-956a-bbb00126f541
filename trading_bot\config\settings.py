"""
Configuration settings for the Solana Trading Bot
"""

import os
from typing import List, Optional
from pydantic import Field
from dotenv import load_dotenv

try:
    from pydantic_settings import BaseSettings
except ImportError:
    # Fallback for older pydantic versions
    from pydantic import BaseSettings

# Load environment variables
load_dotenv()


class TradingConfig(BaseSettings):
    """Trading bot configuration"""
    
    # Solana Configuration
    solana_wallet_path: str = Field(default="./test-wallet.json", env="SOLANA_WALLET_PATH")
    helius_rpc_url: str = Field(default="", env="HELIUS_RPC_URL")
    minimum_buy_amount: float = Field(default=0.015, env="MINIMUM_BUY_AMOUNT")
    priority_fee_base: float = Field(default=0.0003, env="PRIORITY_FEE_BASE")
    
    # Adaptive Trading System
    max_bonding_curve: float = Field(default=40.0, env="MAX_BONDING_CURVE_PROGRESS")
    sell_bonding_curve: float = Field(default=65.0, env="SELL_BONDING_CURVE_PROGRESS")
    max_concurrent_trades: int = Field(default=1, env="MAX_CONCURRENT_TRADES")
    
    # Rate Limiting
    api_rate_limit: int = Field(default=2000, env="API_RATE_LIMIT")  # milliseconds
    balance_check_interval: int = Field(default=120000, env="BALANCE_CHECK_INTERVAL")  # 2 minutes
    spl_token_check_interval: int = Field(default=300000, env="SPL_TOKEN_CHECK_INTERVAL")  # 5 minutes
    
    # Trading Strategy
    buy_threshold_min: float = Field(default=45.0, env="BUY_THRESHOLD_MIN")
    buy_threshold_max: float = Field(default=55.0, env="BUY_THRESHOLD_MAX")
    sell_threshold: float = Field(default=60.0, env="SELL_THRESHOLD")
    min_market_cap_sol: float = Field(default=30.0, env="MIN_MARKET_CAP_SOL")
    
    # Adaptive System
    adjustment_interval: int = Field(default=300000, env="ADJUSTMENT_INTERVAL")  # 5 minutes
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "ignore"


class GMGNConfig(BaseSettings):
    """GMGN Scraper configuration"""
    
    # Scraper Settings
    num_tabs: int = Field(default=6, env="GMGN_NUM_TABS")
    scrape_interval: int = Field(default=800, env="GMGN_SCRAPE_INTERVAL")  # 0.8 seconds
    random_delay_max: int = Field(default=100, env="GMGN_RANDOM_DELAY_MAX")
    
    # Stealth Mode
    user_agent_rotation: bool = Field(default=True, env="GMGN_USER_AGENT_ROTATION")
    viewport_randomization: bool = Field(default=True, env="GMGN_VIEWPORT_RANDOMIZATION")
    mouse_movements: bool = Field(default=True, env="GMGN_MOUSE_MOVEMENTS")
    scroll_simulation: bool = Field(default=True, env="GMGN_SCROLL_SIMULATION")
    resource_blocking: bool = Field(default=True, env="GMGN_RESOURCE_BLOCKING")
    stealth_mode: bool = Field(default=True, env="GMGN_STEALTH_MODE")
    dummy_interactions: bool = Field(default=True, env="GMGN_DUMMY_INTERACTIONS")
    advanced_fingerprint_protection: bool = Field(default=True, env="GMGN_ADVANCED_FINGERPRINT_PROTECTION")
    
    # Browser Settings
    headless: bool = Field(default=True, env="GMGN_HEADLESS")
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "ignore"


class WebSocketConfig(BaseSettings):
    """WebSocket configuration"""
    
    # PumpPortal WebSocket
    url: str = Field(default="wss://pumpportal.fun/api/data", env="WEBSOCKET_URL")
    reconnect_interval: int = Field(default=5000, env="WEBSOCKET_RECONNECT_INTERVAL")
    max_reconnect_attempts: int = Field(default=10, env="WEBSOCKET_MAX_RECONNECT_ATTEMPTS")
    ping_interval: int = Field(default=30000, env="WEBSOCKET_PING_INTERVAL")
    
    # Subscription Settings
    enable_token_creation: bool = Field(default=True, env="WEBSOCKET_ENABLE_TOKEN_CREATION")
    enable_migration: bool = Field(default=True, env="WEBSOCKET_ENABLE_MIGRATION")
    enable_account_trades: bool = Field(default=True, env="WEBSOCKET_ENABLE_ACCOUNT_TRADES")
    enable_token_trades: bool = Field(default=True, env="WEBSOCKET_ENABLE_TOKEN_TRADES")
    
    # Watch Lists
    watch_accounts: List[str] = Field(
        default=["AArPXm8JatJiuyEffuC1un2Sc835SULa4uQqDcaGpAjV"],
        env="WEBSOCKET_WATCH_ACCOUNTS"
    )
    watch_tokens: List[str] = Field(
        default=["91WNez8D22NwBssQbkzjy4s2ipFrzpmn5hfvWVe2aY5p"],
        env="WEBSOCKET_WATCH_TOKENS"
    )
    
    # Auto-unsubscribe
    auto_unsubscribe_enabled: bool = Field(default=False, env="WEBSOCKET_AUTO_UNSUBSCRIBE_ENABLED")
    auto_unsubscribe_delay: int = Field(default=10000, env="WEBSOCKET_AUTO_UNSUBSCRIBE_DELAY")
    dynamic_subscription_management: bool = Field(default=True, env="WEBSOCKET_DYNAMIC_SUBSCRIPTION_MANAGEMENT")
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "ignore"


class LoggingConfig(BaseSettings):
    """Logging configuration"""
    
    # File Logging
    enable_logging: bool = Field(default=True, env="ENABLE_FILE_LOGGING")
    log_directory: str = Field(default="./token_logs", env="LOG_DIRECTORY")
    categorized_coins_file: str = Field(default="categorized_coins.txt", env="CATEGORIZED_COINS_FILE")
    all_coins_file: str = Field(default="all_discovered_coins.txt", env="ALL_COINS_FILE")
    categorized_coins_json: str = Field(default="categorized_coins.json", env="CATEGORIZED_COINS_JSON")
    all_coins_json: str = Field(default="all_discovered_coins.json", env="ALL_COINS_JSON")
    
    # Log Settings
    daily_logs: bool = Field(default=True, env="DAILY_LOGS")
    max_log_size: int = Field(default=10485760, env="MAX_LOG_SIZE")  # 10MB
    backup_old_logs: bool = Field(default=True, env="BACKUP_OLD_LOGS")
    enable_json_logging: bool = Field(default=True, env="ENABLE_JSON_LOGGING")
    
    # Console Logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "ignore"


# Global configuration instances
trading_config = TradingConfig()
gmgn_config = GMGNConfig()
websocket_config = WebSocketConfig()
logging_config = LoggingConfig()
