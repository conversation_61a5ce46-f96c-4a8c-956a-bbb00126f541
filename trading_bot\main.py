#!/usr/bin/env python3
"""
Main Solana Trading Bot Script - Python version
Revolutionary trading bot with GMGN.ai integration and adaptive system
"""

import asyncio
import signal
import sys
import time
from typing import Dict, Any, Set, Optional
from pathlib import Path

from trading_bot.config.settings import trading_config, gmgn_config
from trading_bot.utils.logging import setup_logging, get_logger, trading_logger
from trading_bot.utils.solana_client import SolanaClient
from trading_bot.utils.pump_portal import PumpPortalAPI
from trading_bot.gmgn_scraper import RevolutionaryGMGNScraper
from trading_bot.ui.terminal import TradingBotUI

# Setup logging
setup_logging(log_level="INFO", enable_file_logging=True)
logger = get_logger("main")


class AdaptiveTradingSystem:
    """Adaptive trading system with dynamic threshold adjustment"""
    
    def __init__(self):
        self.max_bonding_curve = trading_config.max_bonding_curve
        self.sell_bonding_curve = trading_config.sell_bonding_curve
        self.successful_trades = 0
        self.failed_trades = 0
        self.total_tokens_analyzed = 0
        self.tokens_under_threshold = 0
        self.last_adjustment = time.time()
        self.adjustment_interval = trading_config.adjustment_interval / 1000  # Convert to seconds
        self.performance_history = []
    
    def update_market_conditions(self, token_data: Dict[str, Any]):
        """Update market conditions with new token data"""
        # This would be expanded with more sophisticated market analysis
        pass
    
    def adapt_thresholds(self):
        """Adapt trading thresholds based on performance"""
        now = time.time()
        
        # Only adjust every adjustment_interval
        if now - self.last_adjustment < self.adjustment_interval:
            return
        
        self.last_adjustment = now
        
        success_rate = self.successful_trades / max(1, self.successful_trades + self.failed_trades)
        opportunity_rate = self.tokens_under_threshold / max(1, self.total_tokens_analyzed)
        
        trading_logger.update_log("🧠 ADAPTIVE ANALYSIS:")
        trading_logger.update_log(f"📈 Success Rate: {success_rate * 100:.1f}%")
        trading_logger.update_log(f"🎯 Opportunity Rate: {opportunity_rate * 100:.1f}%")
        
        old_max_bonding = self.max_bonding_curve
        old_sell_bonding = self.sell_bonding_curve
        
        # Adaptive logic
        if opportunity_rate < 0.05:  # Less than 5% of tokens qualify
            # Market is too hot, increase thresholds
            self.max_bonding_curve = min(60, self.max_bonding_curve + 5)
            self.sell_bonding_curve = min(65, self.sell_bonding_curve + 5)
            trading_logger.update_log("🔥 HOT MARKET: Increasing thresholds to find more opportunities")
        elif opportunity_rate > 0.20:  # More than 20% qualify
            # Market is cool, decrease thresholds
            self.max_bonding_curve = max(20, self.max_bonding_curve - 3)
            self.sell_bonding_curve = max(25, self.sell_bonding_curve - 3)
            trading_logger.update_log("❄️ COOL MARKET: Decreasing thresholds to be more selective")
        
        # Adjust based on success rate
        if success_rate < 0.3 and self.successful_trades + self.failed_trades > 5:
            # Low success rate, be more conservative
            self.max_bonding_curve = max(15, self.max_bonding_curve - 5)
            trading_logger.update_log("⚠️ LOW SUCCESS: Being more conservative")
        
        if old_max_bonding != self.max_bonding_curve or old_sell_bonding != self.sell_bonding_curve:
            trading_logger.update_log("🔄 THRESHOLDS UPDATED:")
            trading_logger.update_log(f"📊 Max Bonding Curve: {old_max_bonding}% → {self.max_bonding_curve}%")
            trading_logger.update_log(f"📈 Sell Bonding Curve: {old_sell_bonding}% → {self.sell_bonding_curve}%")
        else:
            trading_logger.update_log(f"✅ Thresholds remain optimal: Max {self.max_bonding_curve}%, Sell {self.sell_bonding_curve}%")


class SolanaTradingBot:
    """Main Solana trading bot class"""
    
    def __init__(self):
        # Core components
        self.solana_client: Optional[SolanaClient] = None
        self.pump_portal: Optional[PumpPortalAPI] = None
        self.gmgn_scraper: Optional[RevolutionaryGMGNScraper] = None
        self.ui: Optional[TradingBotUI] = None
        
        # Trading state
        self.active_trades: Set[str] = set()
        self.processed_tokens: Set[str] = set()
        self.new_token_queue = []
        self.is_processing_tokens = False
        self.is_running = False
        
        # Adaptive system
        self.adaptive_system = AdaptiveTradingSystem()
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info("🛑 Shutdown signal received")
        self.is_running = False
        asyncio.create_task(self.stop())
    
    async def initialize(self):
        """Initialize all components"""
        try:
            logger.info("🚀 Initializing Solana Trading Bot...")
            
            # Initialize Solana client
            self.solana_client = SolanaClient()
            
            # Initialize PumpPortal API
            self.pump_portal = PumpPortalAPI(self.solana_client)
            
            # Initialize GMGN scraper
            self.gmgn_scraper = RevolutionaryGMGNScraper(
                on_token_found=self._on_token_found,
                on_position_update=self._on_position_update,
                on_websocket_data=self._on_websocket_data
            )
            
            # Initialize UI
            self.ui = TradingBotUI()
            
            logger.success("✅ All components initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize trading bot: {e}")
            raise
    
    async def start(self):
        """Start the trading bot"""
        try:
            self.is_running = True
            
            # Show splash screen
            if not self.ui.show_splash_screen():
                logger.info("🛑 User cancelled startup")
                return
            
            # Start UI
            self.ui.start()
            
            # Check initial balance
            balance = await self.solana_client.get_balance(use_cache=False)
            if balance < trading_config.minimum_buy_amount:
                trading_logger.update_log("❌ Insufficient balance to cover transaction and fees")
                return
            
            # Calculate rent exemption
            rent_exemption = await self.solana_client.calculate_rent_exemption(165)
            if rent_exemption and balance < trading_config.minimum_buy_amount + rent_exemption / 1e9:
                trading_logger.update_log("❌ Insufficient balance to cover rent exemption and transaction")
                return
            
            # Log startup information
            trading_logger.update_log("=" * 80)
            trading_logger.update_log("🚀 ADAPTIVE SOLANA TRADING BOT STARTED")
            trading_logger.update_log(f"📅 Start Time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            trading_logger.update_log(f"💰 Wallet: {self.solana_client.payer.public_key}")
            trading_logger.update_log(f"⚙️  Min Buy Amount: {trading_config.minimum_buy_amount} SOL")
            trading_logger.update_log("🧠 ADAPTIVE SYSTEM ENABLED:")
            trading_logger.update_log(f"📊 Initial Max Bonding Curve: {self.adaptive_system.max_bonding_curve}%")
            trading_logger.update_log(f"📈 Initial Sell Bonding Curve: {self.adaptive_system.sell_bonding_curve}%")
            trading_logger.update_log("🔄 Thresholds adjust every 5 minutes based on market conditions")
            trading_logger.update_log("=" * 80)
            trading_logger.update_log("Starting live trading mode...")
            
            # Update initial account info
            await self._update_account_info()
            self.ui.set_visual_mode("searching")
            
            # Start main trading tasks
            await asyncio.gather(
                self._start_gmgn_trading(),
                self._live_update_account_info(),
                self._process_token_queue_loop(),
                return_exceptions=True
            )
            
        except Exception as e:
            logger.error(f"❌ Error starting trading bot: {e}")
            raise
    
    async def stop(self):
        """Stop the trading bot"""
        logger.info("🛑 Stopping trading bot...")
        self.is_running = False
        
        try:
            # Stop GMGN scraper
            if self.gmgn_scraper:
                await self.gmgn_scraper.stop()
            
            # Close PumpPortal API
            if self.pump_portal:
                await self.pump_portal.close()
            
            # Close Solana client
            if self.solana_client:
                await self.solana_client.close()
            
            # Stop UI
            if self.ui:
                self.ui.stop()
            
            logger.success("✅ Trading bot stopped successfully")
            
        except Exception as e:
            logger.error(f"❌ Error stopping trading bot: {e}")
    
    async def _start_gmgn_trading(self):
        """Start GMGN.ai-based trading"""
        trading_logger.update_log("🚀 Starting Revolutionary GMGN.ai-based trading...")
        
        # Start GMGN scraper
        await self.gmgn_scraper.start()
        
        # Monitor scraper health
        while self.is_running:
            await asyncio.sleep(60)  # Check every 60 seconds
            if not self.gmgn_scraper.is_running:
                trading_logger.update_log("🔄 GMGN Scraper not running, attempting to restart...")
                try:
                    await self.gmgn_scraper.start()
                except Exception as e:
                    logger.error(f"❌ Failed to restart GMGN scraper: {e}")
    
    async def _on_token_found(self, token_data: Dict[str, Any]):
        """Handle new token found by GMGN scraper"""
        if token_data['mint'] not in self.processed_tokens:
            trading_logger.update_log(f"🆕 GMGN.ai: New token detected - {token_data['symbol']} ({token_data['mint'][:8]}...)")
            
            # Update market conditions
            self.adaptive_system.update_market_conditions(token_data)
            
            self.new_token_queue.append(token_data)
            self.processed_tokens.add(token_data['mint'])
    
    async def _on_position_update(self, new_data: Dict[str, Any], old_data: Dict[str, Any]):
        """Handle position update from GMGN scraper"""
        profit_percentage = new_data.get('marketCapSol', 0) / max(old_data.get('marketCapSol', 1), 1)
        if abs(profit_percentage - 1) > 0.05:  # 5% change threshold
            trading_logger.update_log(
                f"📊 Position Update: {new_data.get('symbol', 'Unknown')} - "
                f"Market Cap: {old_data.get('marketCapSol', 0)} → {new_data.get('marketCapSol', 0)} SOL "
                f"({((profit_percentage - 1) * 100):.2f}%)"
            )
    
    async def _on_websocket_data(self, data: Dict[str, Any]):
        """Handle WebSocket data from GMGN scraper"""
        # Process WebSocket data if needed
        pass

    async def _process_token_queue_loop(self):
        """Process tokens from the queue continuously"""
        while self.is_running:
            if not self.is_processing_tokens and self.new_token_queue:
                await self._process_token_queue()
            await asyncio.sleep(2)  # Check every 2 seconds

    async def _process_token_queue(self):
        """Process tokens from the queue"""
        if self.is_processing_tokens or not self.new_token_queue:
            return

        self.is_processing_tokens = True
        trading_logger.update_log(f"🔄 Processing {len(self.new_token_queue)} tokens from queue...")

        while self.new_token_queue and self.is_running:
            token_data = self.new_token_queue.pop(0)
            await self._process_new_token(token_data)
            await asyncio.sleep(2)  # Delay between processing tokens

        self.is_processing_tokens = False
        trading_logger.update_log("✅ Finished processing token queue")

    async def _process_new_token(self, token_data: Dict[str, Any]):
        """Process a new token"""
        try:
            mint = token_data['mint']
            name = token_data.get('name', 'Unknown')
            symbol = token_data.get('symbol', 'UNK')
            market_cap_sol = token_data.get('marketCapSol', 0)

            trading_logger.update_log(f"🔍 Analyzing new token: {symbol} ({mint[:8]}...)")
            trading_logger.update_log(f"📊 Name: {name}, Market Cap: {market_cap_sol} SOL")

            # Calculate bonding curve progress
            bonding_curve_progress = min((market_cap_sol / 69) * 100, 100)
            trading_logger.update_log(f"📈 Estimated bonding curve: {bonding_curve_progress:.1f}%")

            # Update adaptive tracking
            self.adaptive_system.total_tokens_analyzed += 1

            # Run adaptive threshold adjustment
            self.adaptive_system.adapt_thresholds()

            # Check trading constraints
            if len(self.active_trades) >= trading_config.max_concurrent_trades:
                trading_logger.update_log(f"⏸️ Max concurrent trades ({trading_config.max_concurrent_trades}) reached, skipping {symbol}")
                return

            if mint in self.active_trades:
                trading_logger.update_log(f"⏸️ Already trading {symbol}, skipping duplicate")
                return

            # Skip migrated tokens
            if bonding_curve_progress >= 100:
                trading_logger.update_log(f"⏭️ Token {symbol} already migrated to Raydium ({bonding_curve_progress:.1f}%), skipping")
                return

            # REALISTIC BULLETPROOF STRATEGY
            buy_threshold_min = trading_config.buy_threshold_min
            buy_threshold_max = trading_config.buy_threshold_max
            min_market_cap = trading_config.min_market_cap_sol

            # Buy tokens in the sweet spot
            if (buy_threshold_min <= bonding_curve_progress <= buy_threshold_max and
                market_cap_sol > min_market_cap):

                self.adaptive_system.tokens_under_threshold += 1
                trading_logger.update_log(
                    f"🎯 REALISTIC STRATEGY: {symbol} in PROFIT ZONE! "
                    f"Bonding curve: {bonding_curve_progress:.1f}% ({buy_threshold_min}-{buy_threshold_max}% = sweet spot)"
                )

                trading_logger.update_log(f"🔒 ATTEMPTING TRADE: {symbol} ({len(self.active_trades)}/{trading_config.max_concurrent_trades} currently active)")

                # Attempt to buy the token
                self.ui.set_visual_mode("trading")
                success = await self._attempt_token_purchase(mint, symbol, market_cap_sol, bonding_curve_progress)

                if success:
                    self.adaptive_system.successful_trades += 1
                else:
                    self.adaptive_system.failed_trades += 1

                self.ui.set_visual_mode("searching")
            else:
                trading_logger.update_log(
                    f"❌ REALISTIC FILTER: {symbol} not in profit zone: {bonding_curve_progress:.1f}% "
                    f"(need {buy_threshold_min}-{buy_threshold_max}% sweet spot)"
                )

            # Log adaptive stats every 10 tokens
            if self.adaptive_system.total_tokens_analyzed % 10 == 0:
                success_rate = (
                    self.adaptive_system.successful_trades /
                    max(1, self.adaptive_system.successful_trades + self.adaptive_system.failed_trades)
                )
                opportunity_rate = (
                    self.adaptive_system.tokens_under_threshold /
                    self.adaptive_system.total_tokens_analyzed
                )
                trading_logger.update_log(
                    f"📊 ADAPTIVE STATS: {self.adaptive_system.total_tokens_analyzed} analyzed, "
                    f"{opportunity_rate * 100:.1f}% qualify, {success_rate * 100:.1f}% success rate"
                )

            # Update UI stats
            await self._update_ui_stats()

        except Exception as e:
            logger.error(f"❌ Error processing token: {e}")
            self.adaptive_system.failed_trades += 1

    async def _attempt_token_purchase(self, mint: str, symbol: str, market_cap_sol: float, bonding_curve_progress: float) -> bool:
        """Attempt to purchase a token"""
        try:
            trading_logger.update_log(f"⚡ IMMEDIATE BUY: No delay - catching token before Raydium migration!")

            # Attempt to buy with retries
            attempts = 3
            tx_hash = None

            while attempts > 0 and self.is_running:
                tx_hash = await self.pump_portal.buy_token(mint, trading_config.minimum_buy_amount)
                if tx_hash:
                    break
                attempts -= 1
                if attempts > 0:
                    trading_logger.update_log(f"⚡ FAST RETRY: Attempts left: {attempts}")
                    await asyncio.sleep(1)  # 1 second retry delay

            if tx_hash:
                # Add to active trades AFTER successful buy
                self.active_trades.add(mint)
                trading_logger.update_log(f"✅ Successfully bought {symbol}! Transaction: {tx_hash}")
                trading_logger.update_log(f"🔒 TRADE CONFIRMED: {symbol} added to active trades ({len(self.active_trades)}/{trading_config.max_concurrent_trades} active)")

                # Start monitoring this trade
                asyncio.create_task(self._monitor_trade(mint, symbol, market_cap_sol, bonding_curve_progress))

                return True
            else:
                trading_logger.update_log(f"❌ Failed to buy {symbol} after multiple attempts")
                return False

        except Exception as e:
            logger.error(f"❌ Error attempting token purchase: {e}")
            return False

    async def _monitor_trade(self, mint: str, symbol: str, initial_market_cap: float, initial_bonding_curve: float):
        """Monitor a trade for profit/loss conditions"""
        try:
            trading_logger.update_log(f"📊 Starting trade monitoring for {symbol}")
            trading_logger.update_log(f"📈 Initial: Market Cap {initial_market_cap} SOL, Bonding Curve {initial_bonding_curve:.1f}%")

            # Add to GMGN position monitoring
            if self.gmgn_scraper:
                self.gmgn_scraper.add_position_monitoring(mint, {
                    'mint': mint,
                    'symbol': symbol,
                    'marketCapSol': initial_market_cap,
                    'bondingCurve': initial_bonding_curve,
                    'timestamp': time.time()
                })

            # Wait for monitoring period
            await asyncio.sleep(30)  # Wait 30 seconds

            trading_logger.update_log(f"📊 Monitoring period complete for {symbol}. GMGN scraper tracking position updates.")

            # For now, we'll use a simple time-based exit strategy
            # In a real implementation, you'd check current market conditions

            # Check if we should sell based on adaptive thresholds
            current_bonding_curve = initial_bonding_curve  # This would be updated by position monitoring

            if current_bonding_curve >= self.adaptive_system.sell_bonding_curve:
                trading_logger.update_log(f"🎯 Bonding curve reached ADAPTIVE threshold {self.adaptive_system.sell_bonding_curve}%. Selling {symbol}...")
                await self._sell_token(mint, symbol)
            else:
                # Simple profit/loss check (would be enhanced with real market data)
                trading_logger.update_log(f"📊 Holding position for {symbol}. Current profit: estimated based on time")
                # For demo purposes, we'll sell after monitoring period
                await asyncio.sleep(30)  # Additional monitoring time
                await self._sell_token(mint, symbol)

        except Exception as e:
            logger.error(f"❌ Error monitoring trade for {symbol}: {e}")
        finally:
            # Remove from active trades and position monitoring
            self.active_trades.discard(mint)
            if self.gmgn_scraper:
                self.gmgn_scraper.remove_position_monitoring(mint)
            trading_logger.update_log(f"🔓 RELEASED: {symbol} trade monitoring complete ({len(self.active_trades)}/{trading_config.max_concurrent_trades} active)")

    async def _sell_token(self, mint: str, symbol: str):
        """Sell a token"""
        try:
            trading_logger.update_log(f"💸 Selling token {symbol}...")

            # Get current token balance
            tokens = await self.solana_client.get_spl_tokens(use_cache=False)
            token_data = next((token for token in tokens if token['mint'] == mint), None)

            if token_data and token_data['amount'] > 0:
                attempts = 3
                tx_hash = None

                while attempts > 0:
                    tx_hash = await self.pump_portal.sell_token(mint, token_data['amount'])
                    if tx_hash:
                        trading_logger.update_log(f"✅ Sell transaction successful: {tx_hash}")
                        break
                    attempts -= 1
                    trading_logger.update_log(f"Retrying sell transaction... Attempts left: {attempts}")
                    await asyncio.sleep(2)

                if not tx_hash:
                    trading_logger.update_log(f"❌ Failed to sell token {symbol} after multiple attempts")
            else:
                trading_logger.update_log(f"❌ No token balance found for {symbol}")

        except Exception as e:
            logger.error(f"❌ Error selling token {symbol}: {e}")

    async def _update_account_info(self):
        """Update account information"""
        try:
            balance = await self.solana_client.get_balance()
            token_balances = await self.solana_client.get_spl_tokens()

            # Update UI with account info
            if self.ui:
                self.ui.update_stats({
                    "balance": balance,
                    "spl_tokens": token_balances
                })

        except Exception as e:
            logger.error(f"❌ Error updating account info: {e}")

    async def _update_ui_stats(self):
        """Update UI statistics"""
        if self.ui:
            self.ui.update_stats({
                "successful_trades": self.adaptive_system.successful_trades,
                "failed_trades": self.adaptive_system.failed_trades,
                "total_tokens_analyzed": self.adaptive_system.total_tokens_analyzed,
                "tokens_under_threshold": self.adaptive_system.tokens_under_threshold,
                "active_trades": len(self.active_trades),
                "max_concurrent_trades": trading_config.max_concurrent_trades,
                "max_bonding_curve": self.adaptive_system.max_bonding_curve,
                "sell_bonding_curve": self.adaptive_system.sell_bonding_curve
            })

    async def _live_update_account_info(self):
        """Live update account info every 5 minutes"""
        while self.is_running:
            await self._update_account_info()
            await asyncio.sleep(300)  # Update every 5 minutes


async def main():
    """Main function"""
    bot = SolanaTradingBot()

    try:
        # Initialize the bot
        await bot.initialize()

        # Start the bot
        await bot.start()

    except KeyboardInterrupt:
        logger.info("🛑 Bot interrupted by user")
    except Exception as e:
        logger.error(f"❌ Bot failed: {e}")
    finally:
        await bot.stop()


def cli_main():
    """CLI entry point"""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("🛑 Process interrupted")
        sys.exit(1)


if __name__ == "__main__":
    cli_main()
