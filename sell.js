import 'dotenv/config';
import { Keypair, Connection, clusterApiUrl, PublicKey, VersionedTransaction } from '@solana/web3.js';
import { AccountLayout } from '@solana/spl-token';
import fs from 'fs';

const SOLANA_WALLET_PATH = process.env.SOLANA_WALLET_PATH;

let privateKey;
try {
    const keypair = fs.readFileSync(SOLANA_WALLET_PATH, 'utf8');
    const keypairArray = JSON.parse(keypair);

    if (Array.isArray(keypairArray)) {
        privateKey = Uint8Array.from(keypairArray);
        console.log('Private key loaded from keypair file.');
    } else {
        throw new Error('Invalid keypair format');
    }
} catch (error) {
    console.error('Error reading Solana wallet keypair:', error);
    process.exit(1);
}

const payer = Keypair.fromSecretKey(privateKey);
// Use Helius premium RPC to avoid rate limits
const HELIUS_RPC_URL = '';
const connection = new Connection(HELIUS_RPC_URL, {
    commitment: 'confirmed',
    confirmTransactionInitialTimeout: 60000
});

const pumpPortalSell = async (mint, amount) => {
    const url = "https://pumpportal.fun/api/trade-local";
    const data = {
        publicKey: payer.publicKey.toString(),
        action: "sell",
        mint: mint,
        amount: amount.toString(),
        denominatedInSol: "false", // Selling tokens, not SOL
        slippage: 10,
        priorityFee: 0.0003,
        pool: "pump"
    };

    try {
        console.log(`Executing sell transaction for ${amount} tokens of ${mint}`);
        console.log(`🔗 API URL: ${url}`);
        console.log(`📤 Request data: ${JSON.stringify(data)}`);

        const response = await fetch(url, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(data)
        });

        console.log(`📥 API Response Status: ${response.status}`);

        if (response.status === 200) {
            // Get the serialized transaction
            const transactionData = await response.arrayBuffer();
            console.log(`📥 Received transaction data (${transactionData.byteLength} bytes)`);

            // Deserialize the transaction
            const tx = VersionedTransaction.deserialize(new Uint8Array(transactionData));

            // Get a fresh blockhash to avoid "Blockhash not found" errors
            console.log(`🔄 Getting fresh blockhash for sell...`);
            const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('confirmed');
            tx.message.recentBlockhash = blockhash;

            // Sign the transaction with fresh blockhash
            tx.sign([payer]);

            // Send the transaction with high priority and fast confirmation
            console.log(`📤 Sending sell transaction with fresh blockhash and high priority...`);
            const signature = await connection.sendTransaction(tx, {
                maxRetries: 2,
                preflightCommitment: 'processed',
                skipPreflight: false
            });

            // Wait for confirmation with shorter timeout
            console.log(`⏳ Waiting for sell transaction confirmation (15s timeout)...`);
            const confirmationPromise = connection.confirmTransaction({
                signature,
                blockhash,
                lastValidBlockHeight
            }, 'processed'); // Use 'processed' for faster confirmation

            // Add timeout to avoid waiting too long - reduced to 15 seconds
            const timeoutPromise = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Sell transaction confirmation timeout')), 15000)
            );

            const confirmation = await Promise.race([confirmationPromise, timeoutPromise]);

            if (confirmation.value.err) {
                console.log(`❌ Sell transaction failed: ${JSON.stringify(confirmation.value.err)}`);
                return null;
            }

            console.log(`✅ Sell transaction successful: ${signature}`);
            console.log(`🔗 Transaction: https://solscan.io/tx/${signature}`);
            return signature;
        } else {
            const errorText = await response.text();
            console.log(`❌ Sell transaction failed: ${response.status} ${response.statusText}`);
            console.log(`🚨 Error Response: ${errorText}`);
            return null;
        }
    } catch (error) {
        console.log(`❌ Error executing sell transaction: ${error.message}`);
        if (error.stack) {
            console.log(`🚨 Error Stack: ${error.stack}`);
        }
        return null;
    }
};

const fetchSPLTokens = async () => {
    try {
        const tokenAccounts = await connection.getTokenAccountsByOwner(payer.publicKey, { programId: new PublicKey("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA") });
        return tokenAccounts.value.map(accountInfo => {
            const accountData = AccountLayout.decode(accountInfo.account.data);
            return {
                mint: new PublicKey(accountData.mint),
                amount: BigInt(accountData.amount.toString()) // Fetch the raw amount as BigInt
            };
        });
    } catch (error) {
        console.error(`Error fetching SPL tokens: ${error.message}`);
        return [];
    }
};

const sellAllTokens = async () => {
    const tokens = await fetchSPLTokens();
    for (const token of tokens) {
        const mint = token.mint.toString();
        const rawAmount = token.amount;
        const humanReadableAmount = Number(rawAmount) / 10 ** 6; // Convert raw amount to correct human-readable format

        console.log(`Token Mint: ${mint}`);
        console.log(`Raw Amount: ${rawAmount}`);
        console.log(`Human-readable Amount: ${humanReadableAmount}`);

        if (humanReadableAmount >= 1) { // Only proceed if human-readable amount is 1 or more
            console.log(`Selling ${humanReadableAmount} of token ${mint}`);

            let attempts = 5;
            let txHash = null;
            while (attempts > 0) {
                txHash = await pumpPortalSell(mint, humanReadableAmount); // Pass human-readable amount for API
                if (txHash) {
                    console.log(`Sold ${humanReadableAmount} of token ${mint} with transaction hash: ${txHash}`);
                    break;
                } else {
                    console.log(`Retrying sell transaction... Attempts left: ${attempts - 1}`);
                    attempts--;
                    await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds before retrying
                }
            }

            if (!txHash) {
                console.log(`Failed to sell token ${mint} after multiple attempts.`);
            }
        } else {
            console.log(`Skipping token ${mint} as the human-readable amount is less than 1`);
        }
    }
};

sellAllTokens().then(() => {
    console.log('All tokens processed.');
}).catch(error => {
    console.error('Error in selling tokens:', error);
});
