# Solana Trading Bot - Python Version

A secure Solana trading bot for pump.fun tokens, converted from Node.js to Python with enhanced functionality.

## 🚀 Features

### Revolutionary Trading Strategy
- **GMGN.ai Integration**: Advanced scraper with 6 concurrent sessions
- **Adaptive Trading System**: Dynamic threshold adjustment based on market conditions
- **PumpPortal API**: Direct integration for buy/sell transactions
- **Real-time Monitoring**: WebSocket connections for live market data

### Advanced Technology
- **Stealth Mode**: Anti-detection techniques for web scraping
- **Concurrent Processing**: Multiple browser sessions with human behavior simulation
- **Comprehensive Logging**: Detailed file and JSON logging with categorization
- **Terminal UI**: Rich console interface with live statistics

### Trading Logic
- **Buy Strategy**: Target tokens at 45-55% bonding curve (proven momentum zone)
- **Sell Strategy**: Exit at 60% bonding curve (before 69% migration)
- **Risk Management**: 25% profit targets with 10% stop-loss protection
- **Market Adaptation**: Automatic threshold adjustment based on market conditions

## 📋 Requirements

- Python 3.8 or higher
- Solana wallet JSON file
- Environment variables configured
- Playwright browsers installed

## 🛠️ Installation

1. **Clone and setup**:
```bash
git clone <repository-url>
cd solana-trading-bot-python
```

2. **Install dependencies**:
```bash
pip install -r requirements.txt
```

3. **Install Playwright browsers**:
```bash
playwright install
```

4. **Configure environment**:
```bash
cp .env.example .env
# Edit .env with your configuration
```

## ⚙️ Configuration

Create a `.env` file with the following variables:

```env
# Solana Configuration
SOLANA_WALLET_PATH=./test-wallet.json
HELIUS_RPC_URL=your_helius_rpc_url
MINIMUM_BUY_AMOUNT=0.015
MAX_BONDING_CURVE_PROGRESS=40
SELL_BONDING_CURVE_PROGRESS=65

# Trading Configuration
PRIORITY_FEE_BASE=0.0003
MAX_CONCURRENT_TRADES=1

# GMGN Scraper Configuration
GMGN_NUM_TABS=6
GMGN_SCRAPE_INTERVAL=800
GMGN_STEALTH_MODE=true

# Logging Configuration
ENABLE_FILE_LOGGING=true
LOG_DIRECTORY=./token_logs
ENABLE_JSON_LOGGING=true
```

## 🚀 Usage

### Main Trading Bot
```bash
python -m trading_bot.main
# or
solana-trading-bot
```

### Sell All Tokens
```bash
python -m trading_bot.sell
# or
solana-sell-all
```

### Test WebSocket Connection
```bash
python -m trading_bot.test_websocket
# or
test-websocket
```

## 📁 Project Structure

```
trading_bot/
├── __init__.py
├── main.py              # Main trading bot script
├── gmgn_scraper.py      # GMGN.ai scraper with stealth mode
├── sell.py              # Token selling functionality
├── test_websocket.py    # WebSocket testing utilities
├── config/
│   ├── __init__.py
│   └── settings.py      # Configuration management
├── utils/
│   ├── __init__.py
│   ├── solana_client.py # Solana blockchain utilities
│   ├── pump_portal.py   # PumpPortal API integration
│   └── logging.py       # Enhanced logging utilities
└── ui/
    ├── __init__.py
    └── terminal.py      # Terminal UI components
```

## 🔧 Development

### Running Tests
```bash
pytest tests/
```

### Code Formatting
```bash
black trading_bot/
```

### Linting
```bash
flake8 trading_bot/
```

## ⚠️ Disclaimer

This software is for educational purposes only. Trading cryptocurrencies involves substantial risk of loss. Use at your own risk.

## 📄 License

MIT License - see LICENSE file for details.
