#!/usr/bin/env python3
"""
Installation script for Solana Trading Bot Python version
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible"""
    print("🔍 Checking Python version...")
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version {sys.version_info.major}.{sys.version_info.minor} is compatible")
    return True


def install_dependencies():
    """Install Python dependencies"""
    print("📦 Installing Python dependencies...")
    
    # Upgrade pip first
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "Upgrading pip"):
        return False
    
    # Install requirements
    if not run_command(f"{sys.executable} -m pip install -r requirements.txt", "Installing dependencies"):
        return False
    
    return True


def install_playwright():
    """Install Playwright browsers"""
    print("🌐 Installing Playwright browsers...")
    
    if not run_command("playwright install", "Installing Playwright browsers"):
        print("⚠️ Playwright browser installation failed. You may need to install manually:")
        print("   playwright install")
        return False
    
    return True


def setup_environment():
    """Setup environment configuration"""
    print("⚙️ Setting up environment configuration...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        try:
            shutil.copy(env_example, env_file)
            print("✅ Created .env file from .env.example")
            print("⚠️ Please edit .env file with your configuration before running the bot")
        except Exception as e:
            print(f"❌ Failed to create .env file: {e}")
            return False
    elif env_file.exists():
        print("ℹ️ .env file already exists")
    else:
        print("⚠️ No .env.example file found")
    
    return True


def create_directories():
    """Create necessary directories"""
    print("📁 Creating necessary directories...")
    
    directories = [
        "token_logs",
        "logs"
    ]
    
    for directory in directories:
        try:
            Path(directory).mkdir(exist_ok=True)
            print(f"✅ Created directory: {directory}")
        except Exception as e:
            print(f"❌ Failed to create directory {directory}: {e}")
            return False
    
    return True


def verify_installation():
    """Verify the installation"""
    print("🔍 Verifying installation...")
    
    try:
        # Try importing main modules
        import trading_bot
        print("✅ Trading bot module imported successfully")
        
        # Check if Playwright is available
        from playwright.async_api import async_playwright
        print("✅ Playwright is available")
        
        # Check if Solana libraries are available
        from solana.rpc.async_api import AsyncClient
        print("✅ Solana libraries are available")
        
        # Check if WebSocket libraries are available
        import websockets
        print("✅ WebSocket libraries are available")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False


def main():
    """Main installation function"""
    print("🚀 Solana Trading Bot - Python Installation")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies")
        sys.exit(1)
    
    # Install Playwright browsers
    if not install_playwright():
        print("⚠️ Playwright installation had issues, but continuing...")
    
    # Setup environment
    if not setup_environment():
        print("❌ Failed to setup environment")
        sys.exit(1)
    
    # Create directories
    if not create_directories():
        print("❌ Failed to create directories")
        sys.exit(1)
    
    # Verify installation
    if not verify_installation():
        print("❌ Installation verification failed")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 Installation completed successfully!")
    print("\n📋 Next steps:")
    print("1. Edit the .env file with your configuration:")
    print("   - Add your Solana wallet path")
    print("   - Add your Helius RPC URL")
    print("   - Configure trading parameters")
    print("\n2. Run the trading bot:")
    print("   python -m trading_bot.main")
    print("\n3. Or run individual components:")
    print("   python -m trading_bot.sell          # Sell all tokens")
    print("   python -m trading_bot.test_websocket # Test WebSocket")
    print("\n⚠️ Important:")
    print("- Make sure you have sufficient SOL for trading and fees")
    print("- Test with small amounts first")
    print("- This software is for educational purposes only")
    print("\n🔗 For help and documentation, see README.md")


if __name__ == "__main__":
    main()
