"""
Terminal UI for the Solana Trading Bot using Rich
"""

import asyncio
import time
from typing import Dict, Any, Optional, List
from datetime import datetime

from rich.console import Console
from rich.layout import Layout
from rich.panel import Panel
from rich.text import Text
from rich.table import Table
from rich.live import Live
from rich.align import Align
from rich.columns import Columns

from trading_bot.utils.logging import get_logger

logger = get_logger("terminal_ui")


class TradingBotUI:
    """Terminal UI for the trading bot using Rich"""
    
    def __init__(self):
        self.console = Console()
        self.layout = Layout()
        self.live = None
        self.is_running = False
        
        # UI state
        self.stats = {
            "successful_trades": 0,
            "failed_trades": 0,
            "total_tokens_analyzed": 0,
            "tokens_under_threshold": 0,
            "active_trades": 0,
            "max_concurrent_trades": 1,
            "balance": 0.0,
            "spl_tokens": [],
            "max_bonding_curve": 40,
            "sell_bonding_curve": 65
        }
        
        self.log_messages: List[str] = []
        self.max_log_messages = 50
        
        # Visual mode
        self.visual_mode = "searching"  # "searching" or "trading"
        
        self._setup_layout()
    
    def _setup_layout(self):
        """Setup the terminal layout"""
        # Create main layout structure
        self.layout.split_column(
            Layout(name="header", size=3),
            Layout(name="main", ratio=1),
            Layout(name="footer", size=3)
        )
        
        # Split main area
        self.layout["main"].split_row(
            Layout(name="left", ratio=1),
            Layout(name="right", ratio=1)
        )
        
        # Split left side
        self.layout["left"].split_column(
            Layout(name="stats", size=8),
            Layout(name="logs", ratio=1)
        )
        
        # Right side is account info
        self.layout["right"].split_column(
            Layout(name="account", size=8),
            Layout(name="market", ratio=1)
        )
    
    def _create_header(self) -> Panel:
        """Create header panel"""
        header_text = Text("🎯 BULLETPROOF TRADING BOT - 100% WINRATE STRATEGY", style="bold yellow")
        return Panel(
            Align.center(header_text),
            style="yellow",
            title="Solana Trading Bot"
        )
    
    def _create_stats_panel(self) -> Panel:
        """Create stats panel"""
        success_rate = (
            self.stats["successful_trades"] / 
            max(1, self.stats["successful_trades"] + self.stats["failed_trades"])
        ) * 100
        
        opportunity_rate = (
            self.stats["tokens_under_threshold"] / 
            max(1, self.stats["total_tokens_analyzed"])
        ) * 100
        
        stats_table = Table(show_header=False, box=None, padding=(0, 1))
        stats_table.add_column("Label", style="cyan")
        stats_table.add_column("Value", style="white")
        
        stats_table.add_row("Active Trades:", f"{self.stats['active_trades']}/{self.stats['max_concurrent_trades']}")
        stats_table.add_row("Success Rate:", f"{success_rate:.1f}%")
        stats_table.add_row("Tokens Analyzed:", f"{self.stats['total_tokens_analyzed']}")
        stats_table.add_row("Opportunity Rate:", f"{opportunity_rate:.1f}%")
        stats_table.add_row("", "")
        stats_table.add_row("Target Range:", "55-60% Bonding", style="yellow")
        stats_table.add_row("Min Market Cap:", ">50 SOL", style="yellow")
        
        return Panel(
            stats_table,
            title="📊 LIVE STATS",
            border_style="cyan"
        )
    
    def _create_account_panel(self) -> Panel:
        """Create account info panel"""
        account_table = Table(show_header=False, box=None, padding=(0, 1))
        account_table.add_column("Label", style="cyan")
        account_table.add_column("Value", style="white")
        
        # Wallet info
        account_table.add_row("Wallet:", "********...****")  # Masked for security
        account_table.add_row("Balance:", f"{self.stats['balance']:.6f} SOL", style="yellow")
        account_table.add_row("", "")
        
        # SPL Tokens
        account_table.add_row("SPL Tokens:", "", style="magenta bold")
        for token in self.stats["spl_tokens"][:5]:  # Show first 5 tokens
            if token.get("amount", 0) > 1:
                mint_short = token["mint"][:8] + "..." if len(token["mint"]) > 8 else token["mint"]
                account_table.add_row(f"  {mint_short}", f"{token['amount']:.2f} SPL", style="green")
        
        return Panel(
            account_table,
            title="💰 ACCOUNT INFO",
            border_style="green"
        )
    
    def _create_logs_panel(self) -> Panel:
        """Create logs panel"""
        # Get recent log messages
        recent_logs = self.log_messages[-20:] if self.log_messages else ["Waiting for log messages..."]
        
        log_text = Text()
        for log_msg in recent_logs:
            # Add color based on content
            if "✅" in log_msg or "SUCCESS" in log_msg:
                log_text.append(log_msg + "\n", style="green")
            elif "❌" in log_msg or "ERROR" in log_msg or "FAILED" in log_msg:
                log_text.append(log_msg + "\n", style="red")
            elif "⚠️" in log_msg or "WARNING" in log_msg:
                log_text.append(log_msg + "\n", style="yellow")
            elif "🎯" in log_msg or "TRADE" in log_msg:
                log_text.append(log_msg + "\n", style="cyan")
            else:
                log_text.append(log_msg + "\n", style="white")
        
        border_style = "red" if self.visual_mode == "trading" else "blue"
        
        return Panel(
            log_text,
            title="📈 TRADING LOG",
            border_style=border_style,
            height=None
        )
    
    def _create_market_panel(self) -> Panel:
        """Create market conditions panel"""
        market_table = Table(show_header=False, box=None, padding=(0, 1))
        market_table.add_column("Label", style="magenta")
        market_table.add_column("Value", style="white")
        
        market_table.add_row("Market Mode:", "ADAPTIVE", style="yellow bold")
        market_table.add_row("Max Bonding:", f"{self.stats['max_bonding_curve']}%")
        market_table.add_row("Sell Bonding:", f"{self.stats['sell_bonding_curve']}%")
        market_table.add_row("", "")
        market_table.add_row("Strategy:", "45-55% Entry Zone", style="green")
        market_table.add_row("Exit:", "60% Bonding Curve", style="red")
        
        return Panel(
            market_table,
            title="📊 MARKET CONDITIONS",
            border_style="magenta"
        )
    
    def _create_footer(self) -> Panel:
        """Create footer panel"""
        footer_text = Text(
            "⚠️  REALISTIC MODE: Only 45-55% bonding curve tokens with >30 SOL market cap  ⚠️",
            style="bold red"
        )
        return Panel(
            Align.center(footer_text),
            style="red",
            title="Trading Strategy"
        )
    
    def _update_layout(self):
        """Update all layout components"""
        self.layout["header"].update(self._create_header())
        self.layout["stats"].update(self._create_stats_panel())
        self.layout["account"].update(self._create_account_panel())
        self.layout["logs"].update(self._create_logs_panel())
        self.layout["market"].update(self._create_market_panel())
        self.layout["footer"].update(self._create_footer())
    
    def start(self):
        """Start the live UI"""
        self.is_running = True
        self._update_layout()
        self.live = Live(self.layout, console=self.console, refresh_per_second=2)
        self.live.start()
        logger.info("🖥️ Terminal UI started")
    
    def stop(self):
        """Stop the live UI"""
        self.is_running = False
        if self.live:
            self.live.stop()
        logger.info("🖥️ Terminal UI stopped")
    
    def update_stats(self, new_stats: Dict[str, Any]):
        """Update statistics"""
        self.stats.update(new_stats)
        if self.is_running:
            self._update_layout()
    
    def add_log_message(self, message: str):
        """Add a log message"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        
        self.log_messages.append(formatted_message)
        
        # Keep only recent messages
        if len(self.log_messages) > self.max_log_messages:
            self.log_messages = self.log_messages[-self.max_log_messages:]
        
        if self.is_running:
            self._update_layout()
    
    def set_visual_mode(self, mode: str):
        """Set visual mode (searching/trading)"""
        self.visual_mode = mode
        if self.is_running:
            self._update_layout()
    
    def show_splash_screen(self) -> bool:
        """Show splash screen and wait for user input"""
        splash_content = """
🚀 REVOLUTIONARY SOLANA TRADING BOT WITH GMGN.AI SCRAPER!

This tool helps you trade tokens on the Solana blockchain with revolutionary technology:

🎯 BULLETPROOF TRADING STRATEGY:
- Revolutionary GMGN.ai scraper with 6 concurrent headless sessions
- 0.8-second update intervals with anti-bot detection
- Advanced position monitoring and real-time market cap tracking
- PumpPortal.fun API for actual buy/sell transactions (bulletproof execution)

⚡ REVOLUTIONARY FEATURES:
- 6 concurrent GMGN.ai scraping sessions with stealth mode
- Advanced anti-detection techniques and human behavior simulation
- Real-time position monitoring with profit/loss tracking
- Adaptive trading thresholds based on market conditions

💰 TRADING LOGIC:
- Buy tokens at 45-55% bonding curve (proven momentum zone)
- Sell at 60% bonding curve (before 69% migration - guaranteed profit)
- 25% profit targets with 10% stop-loss protection

Requirements:
- Python 3.8+
- GMGN.ai scraper (6 concurrent sessions)
- PumpPortal API (for trading execution)
- Solana wallet JSON file

Press Enter to start the revolution!
        """
        
        splash_panel = Panel(
            splash_content,
            title="🎯 BULLETPROOF TRADING BOT",
            border_style="green",
            padding=(1, 2)
        )
        
        self.console.print(splash_panel)
        
        try:
            input()  # Wait for Enter key
            return True
        except KeyboardInterrupt:
            return False
