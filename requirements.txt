# Solana Trading Bot - Python Dependencies

# Configuration and environment
python-dotenv>=1.0.0
pydantic>=2.5.0
pydantic-settings>=2.0.0

# Terminal UI and logging
rich>=13.7.0
loguru>=0.7.2

# Async programming and WebSocket
websockets>=12.0
aiohttp>=3.9.0
httpx>=0.25.0

# Web scraping and browser automation
playwright>=1.40.0
requests>=2.31.0

# Basic utilities
python-dateutil>=2.8.2

# Core Solana blockchain interaction (optional for testing)
# solana>=0.30.2
# solders>=0.18.1

# Cryptography and encoding
base58>=2.1.1

# JSON handling
orjson>=3.9.0

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
