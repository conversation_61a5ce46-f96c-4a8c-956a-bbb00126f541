# Solana Trading Bot - Python Dependencies
# Core Solana blockchain interaction
solana>=0.30.2
solders>=0.18.1
anchorpy>=0.19.1

# Web scraping and browser automation
playwright>=1.40.0
beautifulsoup4>=4.12.2
selenium>=4.15.0
requests>=2.31.0
aiohttp>=3.9.0

# WebSocket support
websockets>=12.0
websocket-client>=1.6.4

# Terminal UI (equivalent to blessed)
rich>=13.7.0
textual>=0.45.0
urwid>=2.1.2

# Async programming
asyncio
aiofiles>=23.2.1

# Data handling and utilities
pandas>=2.1.0
numpy>=1.24.0
python-dotenv>=1.0.0
pydantic>=2.5.0

# Cryptography and encoding
cryptography>=41.0.0
base58>=2.1.1
nacl>=1.5.0

# HTTP client with advanced features
httpx>=0.25.0

# JSON and data serialization
orjson>=3.9.0

# Logging and monitoring
loguru>=0.7.2

# Date and time handling
python-dateutil>=2.8.2

# Configuration management
pyyaml>=6.0.1
toml>=0.10.2

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0

# Optional: GUI support (alternative to terminal UI)
tkinter-page>=7.6
