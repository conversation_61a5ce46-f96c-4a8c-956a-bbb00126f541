"""
Utility modules for Solana Trading Bot
"""

from .logging import setup_logging, get_logger

# Optional imports that require additional dependencies
try:
    from .solana_client import SolanaClient
    from .pump_portal import PumpPortalAPI
    _optional_imports_available = True
except ImportError:
    SolanaClient = None
    PumpPortalAPI = None
    _optional_imports_available = False

__all__ = ["setup_logging", "get_logger"]

if _optional_imports_available:
    __all__.extend(["SolanaClient", "PumpPortalAPI"])
