# Solana Trading Bot Configuration
# Copy this file to .env and configure your settings

# ===== SOLANA CONFIGURATION =====
# Path to your Solana wallet JSON file
SOLANA_WALLET_PATH=./test-wallet.json

# Helius RPC URL (get from https://helius.xyz)
HELIUS_RPC_URL=https://mainnet.helius-rpc.com/?api-key=YOUR_API_KEY

# Minimum amount in SOL to spend per trade
MINIMUM_BUY_AMOUNT=0.015

# Priority fee for transactions (in SOL)
PRIORITY_FEE_BASE=0.0003

# ===== TRADING STRATEGY =====
# Maximum bonding curve percentage to buy at
MAX_BONDING_CURVE_PROGRESS=40

# Bonding curve percentage to sell at
SELL_BONDING_CURVE_PROGRESS=65

# Maximum number of concurrent trades
MAX_CONCURRENT_TRADES=1

# Buy threshold range (sweet spot for entry)
BUY_THRESHOLD_MIN=45.0
BUY_THRESHOLD_MAX=55.0

# Sell threshold
SELL_THRESHOLD=60.0

# Minimum market cap in SOL
MIN_MARKET_CAP_SOL=30.0

# ===== RATE LIMITING =====
# API rate limit in milliseconds
API_RATE_LIMIT=2000

# Balance check interval in milliseconds (2 minutes)
BALANCE_CHECK_INTERVAL=120000

# SPL token check interval in milliseconds (5 minutes)
SPL_TOKEN_CHECK_INTERVAL=300000

# Adaptive system adjustment interval in milliseconds (5 minutes)
ADJUSTMENT_INTERVAL=300000

# ===== GMGN SCRAPER CONFIGURATION =====
# Number of concurrent browser tabs
GMGN_NUM_TABS=6

# Scraping interval in milliseconds
GMGN_SCRAPE_INTERVAL=800

# Random delay maximum in milliseconds
GMGN_RANDOM_DELAY_MAX=100

# Stealth mode features (true/false)
GMGN_USER_AGENT_ROTATION=true
GMGN_VIEWPORT_RANDOMIZATION=true
GMGN_MOUSE_MOVEMENTS=true
GMGN_SCROLL_SIMULATION=true
GMGN_RESOURCE_BLOCKING=true
GMGN_STEALTH_MODE=true
GMGN_DUMMY_INTERACTIONS=true
GMGN_ADVANCED_FINGERPRINT_PROTECTION=true

# Browser settings
GMGN_HEADLESS=true

# ===== WEBSOCKET CONFIGURATION =====
# PumpPortal WebSocket URL
WEBSOCKET_URL=wss://pumpportal.fun/api/data

# WebSocket reconnection settings
WEBSOCKET_RECONNECT_INTERVAL=5000
WEBSOCKET_MAX_RECONNECT_ATTEMPTS=10
WEBSOCKET_PING_INTERVAL=30000

# Subscription settings (true/false)
WEBSOCKET_ENABLE_TOKEN_CREATION=true
WEBSOCKET_ENABLE_MIGRATION=true
WEBSOCKET_ENABLE_ACCOUNT_TRADES=true
WEBSOCKET_ENABLE_TOKEN_TRADES=true

# Watch lists (comma-separated)
WEBSOCKET_WATCH_ACCOUNTS=AArPXm8JatJiuyEffuC1un2Sc835SULa4uQqDcaGpAjV
WEBSOCKET_WATCH_TOKENS=91WNez8D22NwBssQbkzjy4s2ipFrzpmn5hfvWVe2aY5p

# Auto-unsubscribe settings
WEBSOCKET_AUTO_UNSUBSCRIBE_ENABLED=false
WEBSOCKET_AUTO_UNSUBSCRIBE_DELAY=10000
WEBSOCKET_DYNAMIC_SUBSCRIPTION_MANAGEMENT=true

# ===== LOGGING CONFIGURATION =====
# Enable file logging (true/false)
ENABLE_FILE_LOGGING=true

# Log directory
LOG_DIRECTORY=./token_logs

# Log file names
CATEGORIZED_COINS_FILE=categorized_coins.txt
ALL_COINS_FILE=all_discovered_coins.txt
CATEGORIZED_COINS_JSON=categorized_coins.json
ALL_COINS_JSON=all_discovered_coins.json

# Log settings
DAILY_LOGS=true
MAX_LOG_SIZE=********
BACKUP_OLD_LOGS=true
ENABLE_JSON_LOGGING=true

# Console log level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# ===== ADVANCED SETTINGS =====
# These settings are for advanced users only

# Transaction confirmation timeout (milliseconds)
TRANSACTION_TIMEOUT=15000

# Maximum retries for transactions
MAX_TRANSACTION_RETRIES=3

# Slippage tolerance percentage
DEFAULT_SLIPPAGE=10

# Position monitoring interval (seconds)
POSITION_MONITORING_INTERVAL=30

# Market analysis window (minutes)
MARKET_ANALYSIS_WINDOW=60

# Performance tracking window (trades)
PERFORMANCE_TRACKING_WINDOW=100
