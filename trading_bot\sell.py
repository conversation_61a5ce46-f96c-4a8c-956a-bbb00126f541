#!/usr/bin/env python3
"""
Solana Token Selling Script - Python version
Sells all SPL tokens in the wallet using PumpPortal API
"""

import asyncio
import sys
from typing import Optional

from trading_bot.utils.solana_client import SolanaClient
from trading_bot.utils.pump_portal import PumpPortalAPI
from trading_bot.utils.logging import setup_logging, get_logger
from trading_bot.config.settings import trading_config

# Setup logging
setup_logging(log_level="INFO", enable_file_logging=True)
logger = get_logger("sell_script")


class TokenSeller:
    """Token selling utility class"""
    
    def __init__(self, wallet_path: Optional[str] = None, rpc_url: Optional[str] = None):
        self.wallet_path = wallet_path or trading_config.solana_wallet_path
        self.rpc_url = rpc_url or trading_config.helius_rpc_url
        
        # Initialize clients
        self.solana_client = None
        self.pump_portal = None
    
    async def initialize(self):
        """Initialize the clients"""
        try:
            logger.info("🚀 Initializing Token Seller...")
            
            # Initialize Solana client
            self.solana_client = SolanaClient(
                rpc_url=self.rpc_url,
                wallet_path=self.wallet_path
            )
            
            # Initialize PumpPortal API
            self.pump_portal = PumpPortalAPI(self.solana_client)
            
            logger.success("✅ Token Seller initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Token Seller: {e}")
            raise
    
    async def sell_all_tokens(self):
        """Sell all SPL tokens in the wallet"""
        try:
            logger.info("=" * 80)
            logger.info("🚀 STARTING TOKEN SELLING PROCESS")
            logger.info(f"💰 Wallet: {self.solana_client.payer.public_key}")
            logger.info("=" * 80)
            
            # Check wallet balance first
            balance = await self.solana_client.get_balance(use_cache=False)
            logger.info(f"💰 Current SOL balance: {balance} SOL")
            
            if balance < 0.001:  # Need some SOL for transaction fees
                logger.error("❌ Insufficient SOL balance for transaction fees")
                return False
            
            # Get all SPL tokens
            tokens = await self.solana_client.get_spl_tokens(use_cache=False)
            
            if not tokens:
                logger.info("ℹ️ No SPL tokens found in wallet")
                return True
            
            logger.info(f"🔍 Found {len(tokens)} SPL tokens to process:")
            for i, token in enumerate(tokens, 1):
                logger.info(f"  {i}. {token['mint'][:12]}... - Amount: {token['amount']}")
            
            # Confirm before proceeding
            logger.info("\n⚠️ WARNING: This will sell ALL SPL tokens in your wallet!")
            logger.info("Press Ctrl+C within 5 seconds to cancel...")
            
            try:
                await asyncio.sleep(5)
            except KeyboardInterrupt:
                logger.info("🛑 Operation cancelled by user")
                return False
            
            logger.info("🚀 Starting token selling process...")
            
            # Sell all tokens
            results = await self.pump_portal.sell_all_tokens()
            
            # Display final results
            logger.info("\n" + "=" * 80)
            logger.info("📊 FINAL SELLING RESULTS")
            logger.info("=" * 80)
            logger.info(f"📈 Total tokens processed: {results['total_tokens']}")
            logger.info(f"✅ Successful sells: {results['successful_sells']}")
            logger.info(f"❌ Failed sells: {results['failed_sells']}")
            logger.info(f"⏭️ Skipped tokens: {results['skipped_tokens']}")
            
            if results['transactions']:
                logger.info("\n📋 Transaction Details:")
                for i, tx in enumerate(results['transactions'], 1):
                    status_emoji = "✅" if tx['status'] == 'success' else "❌" if tx['status'] == 'failed' else "⏭️"
                    logger.info(f"  {i}. {status_emoji} {tx['mint'][:12]}... - {tx['amount']} tokens - {tx['status'].upper()}")
                    if tx['signature']:
                        logger.info(f"     🔗 https://solscan.io/tx/{tx['signature']}")
            
            # Final balance check
            final_balance = await self.solana_client.get_balance(use_cache=False)
            balance_change = final_balance - balance
            
            logger.info(f"\n💰 Final SOL balance: {final_balance} SOL")
            if balance_change > 0:
                logger.success(f"📈 Balance increased by: {balance_change:.6f} SOL")
            elif balance_change < 0:
                logger.info(f"📉 Balance decreased by: {abs(balance_change):.6f} SOL (transaction fees)")
            
            success_rate = (results['successful_sells'] / max(1, results['total_tokens'])) * 100
            
            if success_rate == 100:
                logger.success("🎉 ALL TOKENS SOLD SUCCESSFULLY!")
            elif success_rate >= 80:
                logger.success(f"🎊 MOSTLY SUCCESSFUL! {success_rate:.1f}% success rate")
            elif success_rate >= 50:
                logger.warning(f"⚠️ PARTIAL SUCCESS: {success_rate:.1f}% success rate")
            else:
                logger.error(f"❌ LOW SUCCESS RATE: {success_rate:.1f}%")
            
            return success_rate >= 50
            
        except Exception as e:
            logger.error(f"❌ Error during token selling: {e}")
            return False
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.pump_portal:
                await self.pump_portal.close()
            if self.solana_client:
                await self.solana_client.close()
            logger.info("🧹 Cleanup completed")
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")


async def main():
    """Main function"""
    seller = TokenSeller()
    
    try:
        # Initialize
        await seller.initialize()
        
        # Sell all tokens
        success = await seller.sell_all_tokens()
        
        if success:
            logger.success("✅ Token selling completed successfully")
            return 0
        else:
            logger.error("❌ Token selling failed or was cancelled")
            return 1
            
    except KeyboardInterrupt:
        logger.info("🛑 Process interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        return 1
    finally:
        await seller.cleanup()


def cli_main():
    """CLI entry point"""
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("🛑 Process interrupted")
        sys.exit(1)


if __name__ == "__main__":
    cli_main()
