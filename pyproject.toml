[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "solana-trading-bot-python"
version = "1.0.0"
description = "A secure Solana trading bot for pump.fun tokens - Python version"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Secure Trading Bot"}
]
keywords = ["solana", "trading", "bot", "cryptocurrency", "pump.fun", "blockchain"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Financial and Insurance Industry",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Office/Business :: Financial :: Investment",
]
requires-python = ">=3.8"
dependencies = [
    "solana>=0.30.2",
    "solders>=0.18.1",
    "playwright>=1.40.0",
    "websockets>=12.0",
    "rich>=13.7.0",
    "requests>=2.31.0",
    "aiohttp>=3.9.0",
    "python-dotenv>=1.0.0",
    "cryptography>=41.0.0",
    "base58>=2.1.1",
    "loguru>=0.7.2",
    "pydantic>=2.5.0",
    "httpx>=0.25.0",
    "orjson>=3.9.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
]
gui = [
    "textual>=0.45.0",
    "urwid>=2.1.2",
]

[project.scripts]
solana-trading-bot = "trading_bot.main:main"
solana-sell-all = "trading_bot.sell:main"
test-websocket = "trading_bot.test_websocket:main"

[project.urls]
Homepage = "https://github.com/your-repo/solana-trading-bot-python"
Repository = "https://github.com/your-repo/solana-trading-bot-python"
Issues = "https://github.com/your-repo/solana-trading-bot-python/issues"

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"
