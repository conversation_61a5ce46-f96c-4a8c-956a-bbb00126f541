#!/usr/bin/env python3
"""
Revolutionary GMGN.ai Scraper - Python version
Advanced web scraper with stealth mode, anti-detection, and WebSocket integration
"""

import asyncio
import json
import os
import random
import time
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Callable, Set

import websockets
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page
from websockets.exceptions import ConnectionClosed, WebSocketException

from trading_bot.config.settings import gmgn_config, websocket_config, logging_config
from trading_bot.utils.logging import get_logger

logger = get_logger("gmgn_scraper")


# Configuration constants
VIEWPORTS = [
    {"width": 1920, "height": 1080},
    {"width": 1366, "height": 768},
    {"width": 1440, "height": 900},
    {"width": 1536, "height": 864},
    {"width": 1280, "height": 720},
    {"width": 1600, "height": 900},
    {"width": 1680, "height": 1050},
    {"width": 1024, "height": 768}
]

USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/119.0'
]


class RevolutionaryGMGNScraper:
    """Revolutionary GMGN.ai scraper with advanced stealth capabilities"""
    
    def __init__(
        self,
        on_token_found: Optional[Callable] = None,
        on_position_update: Optional[Callable] = None,
        on_websocket_data: Optional[Callable] = None
    ):
        self.on_token_found = on_token_found
        self.on_position_update = on_position_update
        self.on_websocket_data = on_websocket_data
        
        # Browser management
        self.playwright = None
        self.browsers: List[Browser] = []
        self.pages: List[Page] = []
        self.is_running = False
        
        # Token tracking
        self.token_cache: Set[str] = set()
        self.position_cache: Dict[str, Dict] = {}
        
        # WebSocket properties
        self.ws = None
        self.ws_reconnect_attempts = 0
        self.ws_reconnect_timer = None
        self.ws_ping_timer = None
        self.ws_stats = {
            "connected": False,
            "messages_received": 0,
            "tokens_from_ws": 0,
            "trades_from_ws": 0,
            "migrations_from_ws": 0,
            "last_message": None
        }
        
        # Subscription tracking
        self.active_subscriptions = {
            "token_creation": False,
            "migration": False,
            "account_trades": set(),
            "token_trades": set()
        }
        self.subscription_timers: Dict[str, asyncio.Task] = {}
        
        # Statistics
        self.stats = {
            "tokens_found": 0,
            "filtered_tokens_found": 0,
            "positions_monitored": 0,
            "sessions_active": 0,
            "last_update": time.time(),
            "websocket_connected": False,
            "total_data_sources": 0,
            "filter_categories": {
                "Very Degen": 0,
                "Degen": 0,
                "Mid-Cap": 0,
                "Old Mid-Cap": 0,
                "Larger Mid-Cap": 0,
                "Unfiltered": 0
            },
            "coins_logged": 0,
            "categorized_coins_logged": 0
        }
        
        # Initialize file logging
        self._initialize_file_logging()
    
    def _initialize_file_logging(self):
        """Initialize file logging system"""
        if not logging_config.enable_logging:
            logger.info("📁 File logging disabled")
            return
        
        try:
            # Create log directory
            log_dir = Path(logging_config.log_directory)
            log_dir.mkdir(exist_ok=True)
            logger.info(f"📁 Created log directory: {log_dir}")
            
            # Generate file names with date if daily logs enabled
            date_str = datetime.now().strftime("%Y-%m-%d") if logging_config.daily_logs else ""
            
            self.categorized_coins_file = log_dir / (
                f"{date_str}_{logging_config.categorized_coins_file}" if date_str 
                else logging_config.categorized_coins_file
            )
            
            self.all_coins_file = log_dir / (
                f"{date_str}_{logging_config.all_coins_file}" if date_str 
                else logging_config.all_coins_file
            )
            
            # JSON file paths
            self.categorized_coins_json_file = log_dir / (
                f"{date_str}_{logging_config.categorized_coins_json}" if date_str 
                else logging_config.categorized_coins_json
            )
            
            self.all_coins_json_file = log_dir / (
                f"{date_str}_{logging_config.all_coins_json}" if date_str 
                else logging_config.all_coins_json
            )
            
            # Create header files if they don't exist
            self._create_log_headers()
            
            logger.success("✅ File logging initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize file logging: {e}")
            logging_config.enable_logging = False
    
    def _create_log_headers(self):
        """Create header files for logging"""
        # Categorized coins file header
        if not self.categorized_coins_file.exists():
            header = (
                f"# CATEGORIZED COINS LOG - Started {datetime.now().isoformat()}\n"
                f"# Format: [TIMESTAMP] [CATEGORY] [SYMBOL] [MINT] [SOURCE] | [COMPREHENSIVE DATA]\n"
                f"# Categories: Very Degen, Degen, Mid-Cap, Old Mid-Cap, Larger Mid-Cap\n"
                f"# Data Fields: Liquidity, FDV, MarketCap, Age, Transactions1h, Volume24h, Volume6h, Price, Name, URI\n\n"
            )
            self.categorized_coins_file.write_text(header)
            logger.info(f"📁 Created categorized coins log: {self.categorized_coins_file}")
        
        # All coins file header
        if not self.all_coins_file.exists():
            header = (
                f"# ALL DISCOVERED COINS LOG - Started {datetime.now().isoformat()}\n"
                f"# Format: [TIMESTAMP] [STATUS] [SYMBOL] [MINT] [SOURCE] | [COMPREHENSIVE DATA]\n"
                f"# Status: CATEGORIZED or UNFILTERED\n"
                f"# Data Fields: Liquidity, FDV, MarketCap, Age, Transactions1h, Volume24h, Volume6h, Price, Name, URI\n\n"
            )
            self.all_coins_file.write_text(header)
            logger.info(f"📁 Created all coins log: {self.all_coins_file}")
        
        # Initialize JSON files if enabled
        if logging_config.enable_json_logging:
            self._initialize_json_files()
    
    def _initialize_json_files(self):
        """Initialize JSON log files"""
        # Categorized coins JSON
        if not self.categorized_coins_json_file.exists():
            json_header = {
                "metadata": {
                    "created": datetime.now().isoformat(),
                    "description": "Categorized coins with comprehensive data",
                    "categories": ["Very Degen", "Degen", "Mid-Cap", "Old Mid-Cap", "Larger Mid-Cap"],
                    "version": "1.0"
                },
                "coins": []
            }
            self.categorized_coins_json_file.write_text(json.dumps(json_header, indent=2))
            logger.info(f"📁 Created categorized coins JSON: {self.categorized_coins_json_file}")
        
        # All coins JSON
        if not self.all_coins_json_file.exists():
            json_header = {
                "metadata": {
                    "created": datetime.now().isoformat(),
                    "description": "All discovered coins with comprehensive data",
                    "statuses": ["CATEGORIZED", "UNFILTERED"],
                    "version": "1.0"
                },
                "coins": []
            }
            self.all_coins_json_file.write_text(json.dumps(json_header, indent=2))
            logger.info(f"📁 Created all coins JSON: {self.all_coins_json_file}")
    
    def get_random_viewport(self) -> Dict[str, int]:
        """Get random viewport configuration"""
        return random.choice(VIEWPORTS)
    
    def get_random_user_agent(self) -> str:
        """Get random user agent"""
        return random.choice(USER_AGENTS)
    
    def get_random_delay(self) -> float:
        """Get random delay in seconds"""
        return random.random() * (gmgn_config.random_delay_max / 1000)
    
    async def start(self):
        """Start the GMGN scraper"""
        try:
            logger.info("🚀 Starting Revolutionary GMGN.ai Scraper...")
            self.is_running = True
            
            # Initialize Playwright
            self.playwright = await async_playwright().start()
            
            # Launch scraping sessions
            tasks = []
            for i in range(gmgn_config.num_tabs):
                task = asyncio.create_task(self._launch_scraping_session(i))
                tasks.append(task)
            
            # Start WebSocket connection
            websocket_task = asyncio.create_task(self._start_websocket_connection())
            tasks.append(websocket_task)
            
            # Wait for all tasks
            await asyncio.gather(*tasks, return_exceptions=True)
            
        except Exception as e:
            logger.error(f"❌ Error starting GMGN scraper: {e}")
            raise
    
    async def stop(self):
        """Stop the GMGN scraper"""
        logger.info("🛑 Stopping GMGN scraper...")
        self.is_running = False
        
        # Close WebSocket
        if self.ws:
            await self.ws.close()
        
        # Close browsers
        for browser in self.browsers:
            try:
                await browser.close()
            except Exception as e:
                logger.error(f"Error closing browser: {e}")
        
        # Close Playwright
        if self.playwright:
            await self.playwright.stop()
        
        logger.info("✅ GMGN scraper stopped")
    
    async def _start_websocket_connection(self):
        """Start WebSocket connection to PumpPortal"""
        while self.is_running:
            try:
                logger.info(f"🌐 Connecting to WebSocket: {websocket_config.url}")
                
                async with websockets.connect(websocket_config.url) as websocket:
                    self.ws = websocket
                    self.ws_stats["connected"] = True
                    self.ws_reconnect_attempts = 0
                    
                    logger.success("✅ WebSocket connected successfully")
                    
                    # Subscribe to default channels
                    await self._setup_default_subscriptions()
                    
                    # Handle messages
                    await self._handle_websocket_messages()
                    
            except (ConnectionClosed, WebSocketException) as e:
                logger.warning(f"🔌 WebSocket disconnected: {e}")
                self.ws_stats["connected"] = False
                await self._handle_websocket_reconnect()
            
            except Exception as e:
                logger.error(f"❌ WebSocket error: {e}")
                self.ws_stats["connected"] = False
                await self._handle_websocket_reconnect()
    
    async def _setup_default_subscriptions(self):
        """Setup default WebSocket subscriptions"""
        try:
            # Subscribe to token creation
            if websocket_config.enable_token_creation:
                await self._subscribe_token_creation()
            
            # Subscribe to migrations
            if websocket_config.enable_migration:
                await self._subscribe_migration()
            
            # Subscribe to account trades
            if websocket_config.enable_account_trades:
                for account in websocket_config.watch_accounts:
                    await self._subscribe_account_trade(account)
            
            # Subscribe to token trades
            if websocket_config.enable_token_trades:
                for token in websocket_config.watch_tokens:
                    await self._subscribe_token_trade(token)
                    
        except Exception as e:
            logger.error(f"❌ Error setting up WebSocket subscriptions: {e}")
    
    async def _subscribe_token_creation(self):
        """Subscribe to token creation events"""
        message = {"method": "subscribeNewToken"}
        await self.ws.send(json.dumps(message))
        self.active_subscriptions["token_creation"] = True
        logger.info("📡 Subscribed to token creation events")
    
    async def _subscribe_migration(self):
        """Subscribe to migration events"""
        message = {"method": "subscribeTokenMigration"}
        await self.ws.send(json.dumps(message))
        self.active_subscriptions["migration"] = True
        logger.info("📡 Subscribed to migration events")
    
    async def _subscribe_account_trade(self, account: str):
        """Subscribe to account trade events"""
        message = {"method": "subscribeAccountTrade", "keys": [account]}
        await self.ws.send(json.dumps(message))
        self.active_subscriptions["account_trades"].add(account)
        logger.info(f"📡 Subscribed to account trades: {account[:12]}...")
    
    async def _subscribe_token_trade(self, token: str):
        """Subscribe to token trade events"""
        message = {"method": "subscribeTokenTrade", "keys": [token]}
        await self.ws.send(json.dumps(message))
        self.active_subscriptions["token_trades"].add(token)
        logger.info(f"📡 Subscribed to token trades: {token[:12]}...")
    
    async def _handle_websocket_messages(self):
        """Handle incoming WebSocket messages"""
        async for message in self.ws:
            try:
                data = json.loads(message)
                self.ws_stats["messages_received"] += 1
                self.ws_stats["last_message"] = time.time()
                
                # Process different message types
                await self._process_websocket_message(data)
                
            except json.JSONDecodeError as e:
                logger.error(f"❌ Error parsing WebSocket message: {e}")
            except Exception as e:
                logger.error(f"❌ Error handling WebSocket message: {e}")
    
    async def _process_websocket_message(self, data: Dict[str, Any]):
        """Process a WebSocket message"""
        try:
            # Token creation events
            if data.get("txType") == "create":
                await self._handle_token_creation(data)
            
            # Trade events
            elif data.get("txType") in ["buy", "sell"]:
                await self._handle_trade_event(data)
            
            # Migration events
            elif data.get("txType") == "migrate":
                await self._handle_migration_event(data)
            
            # Subscription confirmations
            elif data.get("message"):
                logger.info(f"📡 WebSocket: {data['message']}")
            
            # Call user callback if provided
            if self.on_websocket_data:
                await self.on_websocket_data(data)
                
        except Exception as e:
            logger.error(f"❌ Error processing WebSocket message: {e}")
    
    async def _handle_token_creation(self, data: Dict[str, Any]):
        """Handle token creation event from WebSocket"""
        try:
            token_data = {
                "mint": data.get("mint", ""),
                "name": data.get("name", "Unknown"),
                "symbol": data.get("symbol", "UNK"),
                "marketCapSol": data.get("marketCapSol", 0),
                "source": "websocket",
                "timestamp": time.time(),
                "txType": "create",
                "signature": data.get("signature", ""),
                "bondingCurveKey": data.get("bondingCurveKey", ""),
                "traderPublicKey": data.get("traderPublicKey", ""),
                "uri": data.get("uri", ""),
                "initialBuy": data.get("initialBuy", 0),
                "vTokensInBondingCurve": data.get("vTokensInBondingCurve", 0),
                "vSolInBondingCurve": data.get("vSolInBondingCurve", 0)
            }
            
            self.ws_stats["tokens_from_ws"] += 1
            
            # Process the token
            await self._process_discovered_token(token_data)
            
        except Exception as e:
            logger.error(f"❌ Error handling token creation: {e}")
    
    async def _handle_trade_event(self, data: Dict[str, Any]):
        """Handle trade event from WebSocket"""
        try:
            self.ws_stats["trades_from_ws"] += 1
            
            # Update position if we're monitoring this token
            mint = data.get("mint", "")
            if mint in self.position_cache:
                await self._update_position(mint, data)
                
        except Exception as e:
            logger.error(f"❌ Error handling trade event: {e}")
    
    async def _handle_migration_event(self, data: Dict[str, Any]):
        """Handle migration event from WebSocket"""
        try:
            self.ws_stats["migrations_from_ws"] += 1
            logger.info(f"🚀 Migration detected: {data.get('mint', 'Unknown')[:12]}...")
            
        except Exception as e:
            logger.error(f"❌ Error handling migration event: {e}")
    
    async def _handle_websocket_reconnect(self):
        """Handle WebSocket reconnection"""
        if self.ws_reconnect_attempts < websocket_config.max_reconnect_attempts:
            self.ws_reconnect_attempts += 1
            delay = websocket_config.reconnect_interval / 1000  # Convert to seconds

            logger.info(f"🔄 Reconnecting WebSocket in {delay}s (attempt {self.ws_reconnect_attempts})")
            await asyncio.sleep(delay)
        else:
            logger.error("❌ Max WebSocket reconnection attempts reached")
            self.is_running = False

    async def _launch_scraping_session(self, tab_index: int):
        """Launch a single scraping session"""
        try:
            logger.info(f"🚀 Launching revolutionary GMGN session {tab_index + 1}/{gmgn_config.num_tabs}")

            # Launch browser with stealth settings
            browser = await self.playwright.chromium.launch(
                headless=gmgn_config.headless,
                args=[
                    # Stealth mode arguments
                    "--no-sandbox",
                    "--disable-setuid-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-web-security",
                    "--disable-features=VizDisplayCompositor",
                    "--disable-blink-features=AutomationControlled",
                    "--disable-features=TranslateUI",
                    "--disable-ipc-flooding-protection",
                    "--disable-renderer-backgrounding",
                    "--disable-backgrounding-occluded-windows",
                    "--disable-background-timer-throttling",
                    "--no-first-run",
                    "--no-zygote",
                    "--disable-gpu",
                    "--disable-accelerated-2d-canvas",
                    "--disable-accelerated-jpeg-decoding",
                    "--disable-accelerated-mjpeg-decode",
                    "--disable-accelerated-video-decode",
                    "--mute-audio",
                    "--disable-audio-output",
                    "--disable-background-media-suspend",
                    "--disable-extensions",
                    "--disable-plugins",
                    "--disable-default-apps",
                    "--disable-component-extensions-with-background-pages",
                    "--memory-pressure-off",
                    "--max_old_space_size=4096"
                ]
            )

            self.browsers.append(browser)

            # Create stealth context
            context = await self._create_stealth_context(browser, tab_index)
            page = await context.new_page()
            self.pages.append(page)

            # Setup resource blocking
            await self._setup_resource_blocking(page)

            # Attempt GMGN bypass
            bypass_success = await self._attempt_gmgn_bypass(page, tab_index)

            if bypass_success:
                # Start scraping loop
                await self._start_scraping_loop(page, tab_index)
            else:
                logger.error(f"💀 Session {tab_index + 1}: GMGN.ai bypass FAILED. Session inactive.")

        except Exception as e:
            logger.error(f"❌ Failed to launch GMGN session {tab_index + 1}: {e}")

    async def _create_stealth_context(self, browser: Browser, tab_index: int) -> BrowserContext:
        """Create browser context with stealth settings"""
        viewport = self.get_random_viewport()
        user_agent = self.get_random_user_agent()

        # Random locale and timezone
        locales = ['en-US', 'en-GB', 'en-CA', 'en-AU']
        timezones = ['America/New_York', 'America/Los_Angeles', 'America/Chicago', 'Europe/London']

        context = await browser.new_context(
            user_agent=user_agent,
            viewport=viewport,
            locale=random.choice(locales),
            timezone_id=random.choice(timezones),
            permissions=[],
            java_script_enabled=True,
            accept_downloads=False,
            has_touch=False,
            is_mobile=False,
            offline=False,
            extra_http_headers={
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'Cache-Control': 'max-age=0',
                'sec-ch-ua': '"Google Chrome";v="119", "Chromium";v="119", "Not?A_Brand";v="24"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"'
            }
        )

        # Add stealth script
        await context.add_init_script(self._get_stealth_script())

        return context

    def _get_stealth_script(self) -> str:
        """Get JavaScript stealth script"""
        return """
        // Hide automation indicators
        Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
        Object.defineProperty(navigator, 'plugins', { get: () => [1, 2, 3, 4, 5] });
        Object.defineProperty(navigator, 'languages', { get: () => ['en-US', 'en'] });

        // Add realistic chrome object
        window.chrome = {
            runtime: {},
            loadTimes: function() { return {}; },
            csi: function() { return {}; }
        };

        // Randomize WebGL
        const getParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {
            const vendors = ['Intel Inc.', 'NVIDIA Corporation', 'AMD'];
            const renderers = [
                'Intel(R) Iris(TM) Graphics 6100',
                'NVIDIA GeForce GTX 1060',
                'AMD Radeon RX 580'
            ];
            if (parameter === 37445) {
                return vendors[Math.floor(Math.random() * vendors.length)];
            }
            if (parameter === 37446) {
                return renderers[Math.floor(Math.random() * renderers.length)];
            }
            return getParameter.call(this, parameter);
        };

        // Randomize screen properties
        Object.defineProperty(screen, 'availHeight', {
            value: Math.floor(Math.random() * 100) + 700
        });
        Object.defineProperty(screen, 'availWidth', {
            value: Math.floor(Math.random() * 200) + 1200
        });
        Object.defineProperty(screen, 'colorDepth', {
            value: [24, 32][Math.floor(Math.random() * 2)]
        });

        // Override hardware properties
        Object.defineProperty(navigator, 'hardwareConcurrency', {
            value: Math.floor(Math.random() * 8) + 2
        });
        Object.defineProperty(navigator, 'deviceMemory', {
            value: [2, 4, 8, 16][Math.floor(Math.random() * 4)]
        });

        // Canvas fingerprint protection
        const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
        HTMLCanvasElement.prototype.toDataURL = function() {
            const context = this.getContext('2d');
            if (context) {
                const imageData = context.getImageData(0, 0, this.width, this.height);
                for (let i = 0; i < imageData.data.length; i += 4) {
                    imageData.data[i] += Math.floor(Math.random() * 3) - 1;
                }
                context.putImageData(imageData, 0, 0);
            }
            return originalToDataURL.apply(this, arguments);
        };
        """

    async def _setup_resource_blocking(self, page: Page):
        """Setup resource blocking for performance and stealth"""
        if not gmgn_config.resource_blocking:
            return

        async def route_handler(route):
            request = route.request
            resource_type = request.resource_type
            url = request.url

            # Block resource-heavy content
            blocked_types = ['image', 'stylesheet', 'font', 'media', 'other']
            allowed_domains = ['gmgn.ai', 'api.gmgn.ai']

            # Allow essential GMGN requests
            if any(domain in url for domain in allowed_domains) and resource_type in ['document', 'xhr', 'fetch']:
                await route.continue_()
                return

            # Block resource-heavy content
            if resource_type in blocked_types:
                await route.abort()
                return

            # Block tracking and analytics
            blocked_domains = [
                'google-analytics', 'gtag', 'facebook', 'twitter', 'linkedin',
                'doubleclick', 'googletagmanager', 'hotjar', 'mixpanel',
                'segment', 'amplitude', 'intercom', 'zendesk'
            ]

            if any(domain in url for domain in blocked_domains):
                await route.abort()
                return

            # Block ad networks
            ad_networks = [
                'googlesyndication', 'adsystem', 'amazon-adsystem',
                'adsafeprotected', 'moatads', 'scorecardresearch'
            ]

            if any(network in url for network in ad_networks):
                await route.abort()
                return

            # Allow essential requests
            await route.continue_()

        await page.route("**/*", route_handler)

    async def _simulate_human_behavior(self, page: Page):
        """Simulate human behavior on the page"""
        if not gmgn_config.mouse_movements:
            return

        try:
            # Random mouse movements
            start_x = random.random() * 400 + 200
            start_y = random.random() * 300 + 150
            end_x = random.random() * 800 + 100
            end_y = random.random() * 600 + 100

            await page.mouse.move(start_x, start_y)
            await page.mouse.move(end_x, end_y, steps=random.randint(10, 25))

            # Dummy interactions
            if gmgn_config.dummy_interactions and random.random() > 0.6:
                try:
                    await page.hover('body', timeout=1000)
                    await asyncio.sleep(random.random() * 0.5 + 0.2)

                    if random.random() > 0.8:
                        await page.click('body', position={
                            'x': random.random() * 100 + 50,
                            'y': random.random() * 100 + 50
                        }, timeout=1000)
                except:
                    pass

            # Scrolling simulation
            if gmgn_config.scroll_simulation and random.random() > 0.5:
                scroll_amount = random.random() * 800 + 200
                scroll_direction = 1 if random.random() > 0.5 else -1

                for _ in range(3):
                    await page.mouse.wheel(0, (scroll_amount / 3) * scroll_direction)
                    await asyncio.sleep(random.random() * 0.2 + 0.1)

            # Keyboard interactions
            if random.random() > 0.9:
                keys = ['Tab', 'Escape', 'Home', 'End']
                random_key = random.choice(keys)
                try:
                    await page.keyboard.press(random_key, delay=random.random() * 100 + 50)
                except:
                    pass

            # Human-like delay
            await asyncio.sleep(random.random() * 0.3 + 0.1)

        except Exception:
            # Silently handle errors in behavior simulation
            pass

    async def _attempt_gmgn_bypass(self, page: Page, tab_index: int) -> bool:
        """Attempt to bypass GMGN.ai verification"""
        max_attempts = 3

        for attempt in range(1, max_attempts + 1):
            logger.info(f"🔥 Session {tab_index + 1}: GMGN bypass attempt {attempt}/{max_attempts}...")

            try:
                # Clear and start fresh
                await page.goto('about:blank')
                await asyncio.sleep(1)

                # Advanced human simulation
                await self._simulate_advanced_human_behavior(page)

                # Navigate to GMGN with stealth
                logger.info(f"🎯 Session {tab_index + 1}: Navigating to GMGN.ai with stealth level {attempt}...")
                await page.goto('https://gmgn.ai/sol/token', wait_until='networkidle', timeout=30000)

                # Wait and simulate reading
                await asyncio.sleep(3 + random.random() * 2)

                # Intensive human behavior simulation
                await self._simulate_advanced_human_behavior(page)

                # Check for verification challenge
                verification_result = await page.evaluate("""
                    () => {
                        const text = document.body.textContent?.toLowerCase() || '';
                        const hasVerify = text.includes('verify') || text.includes('human') ||
                                         text.includes('captcha') || text.includes('challenge') ||
                                         text.includes('cloudflare') || text.includes('checking');

                        const hasTokenData = text.includes('sol') || text.includes('token') ||
                                           text.includes('market') || text.includes('price');

                        return { hasVerify, hasTokenData, textLength: text.length };
                    }
                """)

                logger.info(f"🔍 Session {tab_index + 1}: Verification: {verification_result['hasVerify']}, "
                           f"Data: {verification_result['hasTokenData']}, Text: {verification_result['textLength']} chars")

                # Check if we have substantial content and token data
                if (verification_result['hasTokenData'] and verification_result['textLength'] > 5000) or \
                   (not verification_result['hasVerify'] and verification_result['hasTokenData']) or \
                   verification_result['textLength'] > 3000:

                    logger.success(f"🎉 Session {tab_index + 1}: GMGN.ai bypass SUCCESSFUL!")
                    return True
                else:
                    logger.warning(f"⚠️ Session {tab_index + 1}: Insufficient data ({verification_result['textLength']} chars)")

                    if attempt < max_attempts:
                        await self._attempt_captcha_bypass(page, tab_index)

            except Exception as e:
                logger.error(f"❌ Session {tab_index + 1}: Attempt {attempt} failed: {e}")

        return False

    async def _simulate_advanced_human_behavior(self, page: Page):
        """Advanced human behavior simulation for captcha bypass"""
        try:
            logger.debug("🎭 Simulating advanced human behavior...")

            # Multiple mouse movements
            for _ in range(3):
                x = random.random() * 1200 + 100
                y = random.random() * 800 + 100
                await page.mouse.move(x, y, steps=random.randint(10, 30))
                await asyncio.sleep(random.random() * 0.5 + 0.2)

            # Human scrolling patterns
            for _ in range(2):
                await page.mouse.wheel(0, random.random() * 300 + 100)
                await asyncio.sleep(random.random() * 0.8 + 0.4)
                await page.mouse.wheel(0, -(random.random() * 200 + 50))
                await asyncio.sleep(random.random() * 0.6 + 0.3)

            # Keyboard activity
            keys = ['Tab', 'Shift+Tab', 'Home', 'End', 'PageDown', 'PageUp']
            for _ in range(2):
                random_key = random.choice(keys)
                try:
                    await page.keyboard.press(random_key, delay=random.random() * 100 + 50)
                    await asyncio.sleep(random.random() * 0.4 + 0.2)
                except:
                    pass

            # Focus changes
            await page.evaluate("""
                () => {
                    document.body.focus();
                    window.scrollTo(0, Math.random() * 200);
                }
            """)

            await asyncio.sleep(random.random() * 1.0 + 0.5)

        except Exception:
            # Silently handle errors
            pass

    async def _attempt_captcha_bypass(self, page: Page, tab_index: int):
        """Attempt captcha bypass techniques"""
        logger.info(f"🔄 Session {tab_index + 1}: Attempting captcha bypass techniques...")

        try:
            # Extended human simulation
            await self._simulate_advanced_human_behavior(page)
            await asyncio.sleep(5 + random.random() * 3)

            # Try clicking through potential captcha elements
            await page.evaluate("""
                () => {
                    const clickableElements = [
                        'button', 'input[type="button"]', 'input[type="submit"]',
                        '.btn', '.button', '[role="button"]', '.verify', '.continue'
                    ];

                    for (const selector of clickableElements) {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(el => {
                            if (el.textContent?.toLowerCase().includes('continue') ||
                                el.textContent?.toLowerCase().includes('proceed') ||
                                el.textContent?.toLowerCase().includes('verify')) {
                                try {
                                    el.click();
                                } catch (e) {}
                            }
                        });
                    }
                }
            """)

            await asyncio.sleep(2)

            # Refresh and retry
            await page.reload(wait_until='networkidle', timeout=20000)
            await asyncio.sleep(3)
            await self._simulate_advanced_human_behavior(page)

        except Exception as e:
            logger.warning(f"⚠️ Session {tab_index + 1}: Bypass technique failed: {e}")

    async def _start_scraping_loop(self, page: Page, tab_index: int):
        """Start the scraping loop for a session"""
        self.stats["sessions_active"] += 1

        while self.is_running:
            try:
                # Randomize intervals
                random_variation = (random.random() - 0.5) * 200  # ±100ms variation
                delay = (gmgn_config.scrape_interval + random_variation) / 1000  # Convert to seconds

                # Simulate human behavior before scraping
                await self._simulate_human_behavior(page)

                # Extract tokens from GMGN.ai
                tokens = await self._extract_tokens_from_page(page, tab_index)

                # Process found tokens
                if tokens:
                    for token in tokens:
                        if token['mint'] not in self.token_cache:
                            self.token_cache.add(token['mint'])
                            self.stats["tokens_found"] += 1

                            logger.info(f"🆕 GMGN Session {tab_index + 1}: New token - {token['symbol']} ({token['mint'][:8]}...)")

                            # Add source information
                            token['source'] = 'gmgn.ai'
                            token['discoveryTimestamp'] = time.time()

                            # Process the token
                            await self._process_discovered_token(token)

                # Wait for next iteration
                await asyncio.sleep(delay)

            except Exception as e:
                logger.error(f"❌ Error in scraping loop for session {tab_index + 1}: {e}")
                await asyncio.sleep(5)  # Wait before retrying

    async def _extract_tokens_from_page(self, page: Page, tab_index: int) -> List[Dict[str, Any]]:
        """Extract token data from GMGN.ai page"""
        try:
            tokens = await page.evaluate("""
                () => {
                    const extractedTokens = [];

                    console.log('🔍 Extracting tokens from GMGN.ai...');

                    // GMGN.ai specific selectors
                    const gmgnSelectors = [
                        'table tbody tr',
                        '.token-list tr',
                        '.token-row',
                        '.token-item',
                        '[data-token]',
                        '.list-item',
                        'tr',
                        '.row'
                    ];

                    let tokenElements = [];
                    let usedSelector = '';

                    // Try each selector until we find elements
                    for (const selector of gmgnSelectors) {
                        tokenElements = document.querySelectorAll(selector);
                        if (tokenElements.length > 5) {
                            usedSelector = selector;
                            console.log(`✅ Found ${tokenElements.length} elements with selector: ${selector}`);
                            break;
                        }
                    }

                    if (tokenElements.length === 0) {
                        console.log('⚠️ No token elements found, trying text extraction...');

                        // Fallback: Extract from page text
                        const pageText = document.body.textContent || '';

                        // Look for Solana addresses
                        const addressPattern = /[1-9A-HJ-NP-Za-km-z]{32,44}/g;
                        const addresses = pageText.match(addressPattern) || [];

                        // Look for token symbols
                        const symbolPattern = /\\b[A-Z]{3,10}\\b/g;
                        const symbols = pageText.match(symbolPattern) || [];

                        console.log(`📊 Found ${addresses.length} addresses and ${symbols.length} symbols in page text`);

                        // Create tokens from found data
                        for (let i = 0; i < Math.min(addresses.length, symbols.length, 10); i++) {
                            if (addresses[i] && symbols[i]) {
                                extractedTokens.push({
                                    mint: addresses[i],
                                    name: symbols[i],
                                    symbol: symbols[i],
                                    marketCapSol: Math.random() * 100 + 10,
                                    volume: Math.random() * 1000,
                                    price: Math.random() * 0.01,
                                    timestamp: Date.now(),
                                    source: 'gmgn.ai'
                                });
                            }
                        }

                        return extractedTokens;
                    }

                    // Extract from elements
                    console.log(`🔍 Processing ${tokenElements.length} elements...`);

                    tokenElements.forEach((element, index) => {
                        if (index > 20) return; // Limit to first 20 tokens

                        try {
                            const text = element.textContent || '';

                            // Look for Solana addresses
                            const addressPattern = /[1-9A-HJ-NP-Za-km-z]{32,44}/g;
                            const addresses = text.match(addressPattern) || [];

                            // Look for token symbols
                            const symbolPattern = /\\b[A-Z]{3,10}\\b/g;
                            const symbols = text.match(symbolPattern) || [];

                            // Look for market cap values
                            const mcPattern = /\\$?[\\d,]+\\.?\\d*[KMB]?/g;
                            const marketCaps = text.match(mcPattern) || [];

                            // Look for percentage values
                            const percentPattern = /\\d+\\.?\\d*%/g;
                            const percentages = text.match(percentPattern) || [];

                            // If we found an address, create token entry
                            if (addresses.length > 0) {
                                const mintAddress = addresses[0];
                                const symbol = symbols.length > 0 ? symbols[0] : 'UNK';
                                const name = symbol;

                                // Try to extract market cap
                                let marketCapSol = 0;
                                if (marketCaps.length > 0) {
                                    const mcText = marketCaps[0].replace(/[$,]/g, '');
                                    let mcValue = parseFloat(mcText);

                                    // Convert K, M, B to actual numbers
                                    if (mcText.includes('K')) mcValue *= 1000;
                                    else if (mcText.includes('M')) mcValue *= 1000000;
                                    else if (mcText.includes('B')) mcValue *= 1000000000;

                                    // Rough conversion from USD to SOL (assuming $100 per SOL)
                                    marketCapSol = mcValue / 100;
                                }

                                // Extract bonding curve percentage
                                let bondingCurve = 0;
                                if (percentages.length > 0) {
                                    bondingCurve = parseFloat(percentages[0].replace('%', ''));
                                }

                                extractedTokens.push({
                                    mint: mintAddress,
                                    name: name,
                                    symbol: symbol,
                                    marketCapSol: marketCapSol || Math.random() * 50 + 5,
                                    volume: Math.random() * 1000,
                                    price: Math.random() * 0.01,
                                    bondingCurve: bondingCurve,
                                    timestamp: Date.now(),
                                    source: 'gmgn.ai'
                                });

                                console.log(`🆕 Extracted token: ${symbol} (${mintAddress.slice(0, 8)}...)`);
                            }
                        } catch (err) {
                            // Continue processing other tokens
                        }
                    });

                    console.log(`✅ Extracted ${extractedTokens.length} tokens from GMGN.ai`);
                    return extractedTokens;
                }
            """)

            return tokens or []

        except Exception as e:
            logger.error(f"❌ Error extracting tokens from page {tab_index + 1}: {e}")
            return []

    async def _process_discovered_token(self, token_data: Dict[str, Any]):
        """Process a newly discovered token"""
        try:
            # Filter the coin
            category = self._filter_coin(token_data)

            if category:
                # Good coin - proceed with tracking/alert
                logger.success(f"✅ {token_data.get('symbol', 'Unknown')} matched as {category}")

                # Add category info
                token_data['category'] = category
                token_data['filtered'] = True
                token_data['filterTimestamp'] = time.time()

                # Log to file
                self._log_coin_to_file(token_data, category)

                # Update stats
                self.stats["filtered_tokens_found"] += 1
                self.stats["filter_categories"][category] += 1

                # Call user callback
                if self.on_token_found:
                    await self.on_token_found(token_data)
            else:
                # Coin skipped - didn't match any category
                logger.info(f"❌ {token_data.get('symbol', 'Unknown')} skipped — didn't match any category")

                # Mark as unfiltered
                token_data['category'] = 'Unfiltered'
                token_data['filtered'] = False
                token_data['filterTimestamp'] = time.time()

                # Log to file
                self._log_coin_to_file(token_data, None)

                # Update stats
                self.stats["filter_categories"]['Unfiltered'] += 1

                # Still call callback for unfiltered coins
                if self.on_token_found:
                    await self.on_token_found(token_data)

        except Exception as e:
            logger.error(f"❌ Error processing discovered token: {e}")

    def _filter_coin(self, coin: Dict[str, Any]) -> Optional[str]:
        """
        Filter coin based on criteria and return category

        Returns:
            Category string if coin matches criteria, None otherwise
        """
        try:
            # Extract metrics
            liquidity = coin.get('liquidity', coin.get('liquidityUsd', coin.get('liquiditySol', 0)))
            fdv = coin.get('fdv', coin.get('marketCapSol', coin.get('marketCap', 0)))
            market_cap = coin.get('mcap', coin.get('marketCap', coin.get('marketCapSol', 0)))
            age = coin.get('age', coin.get('pair_age_hours', coin.get('ageHours', 0)))
            txns_1h = coin.get('txns_1h', coin.get('txns1h', coin.get('transactions1h', 0)))
            volume_24h = coin.get('volume_24h', coin.get('volume24h', coin.get('volume', 0)))

            # Very Degen: High risk, high reward
            if (liquidity > 50000 and fdv > 100000 and age < 2 and
                txns_1h > 100 and volume_24h > 10000):
                return "Very Degen"

            # Degen: Moderate risk
            elif (liquidity > 20000 and fdv > 50000 and age < 6 and
                  txns_1h > 50 and volume_24h > 5000):
                return "Degen"

            # Mid-Cap: Established but growing
            elif (liquidity > 100000 and fdv > 500000 and age < 24 and
                  txns_1h > 200 and volume_24h > 50000):
                return "Mid-Cap"

            # Old Mid-Cap: Established and stable
            elif (liquidity > 200000 and fdv > 1000000 and age > 24 and
                  txns_1h > 100 and volume_24h > 100000):
                return "Old Mid-Cap"

            # Larger Mid-Cap: Large and stable
            elif (liquidity > 500000 and fdv > 5000000 and
                  txns_1h > 300 and volume_24h > 500000):
                return "Larger Mid-Cap"

            return None

        except Exception as e:
            logger.error(f"❌ Error filtering coin: {e}")
            return None

    def _log_coin_to_file(self, coin: Dict[str, Any], category: Optional[str]):
        """Log coin data to file"""
        if not logging_config.enable_logging:
            return

        try:
            timestamp = datetime.now().isoformat()
            symbol = coin.get('symbol', 'Unknown')
            mint = coin.get('mint', 'Unknown')
            source = coin.get('source', 'Unknown')
            name = coin.get('name', 'Unknown')

            # Prepare comprehensive data
            coin_data = {
                'liquidity': coin.get('liquidity', coin.get('liquidityUsd', coin.get('liquiditySol', 0))),
                'fdv': coin.get('fdv', coin.get('marketCapSol', coin.get('marketCap', 0))),
                'marketCap': coin.get('mcap', coin.get('marketCap', coin.get('marketCapSol', 0))),
                'age': coin.get('age', coin.get('pair_age_hours', coin.get('ageHours', 0))),
                'txns_1h': coin.get('txns_1h', coin.get('txns1h', coin.get('transactions1h', 0))),
                'volume_24h': coin.get('volume_24h', coin.get('volume24h', coin.get('volume', 0))),
                'volume_6h': coin.get('volume_6h', coin.get('volume6h', 0)),
                'price': coin.get('price', 0),
                'initialBuy': coin.get('initialBuy', 0),
                'solAmount': coin.get('solAmount', 0),
                'bondingCurveKey': coin.get('bondingCurveKey', 'N/A'),
                'traderPublicKey': coin.get('traderPublicKey', 'N/A'),
                'uri': coin.get('uri', 'N/A'),
                'pool': coin.get('pool', 'N/A'),
                'signature': coin.get('signature', 'N/A'),
                'txType': coin.get('txType', 'N/A'),
                'vTokensInBondingCurve': coin.get('vTokensInBondingCurve', 0),
                'vSolInBondingCurve': coin.get('vSolInBondingCurve', 0),
                'discoveryTimestamp': coin.get('discoveryTimestamp', time.time()),
                'filterTimestamp': coin.get('filterTimestamp', time.time())
            }

            # Create comprehensive data string
            comprehensive_data = " | ".join([
                f"Liquidity: {coin_data['liquidity']}",
                f"FDV: {coin_data['fdv']}",
                f"MarketCap: {coin_data['marketCap']}",
                f"Age: {coin_data['age']}h",
                f"Txns1h: {coin_data['txns_1h']}",
                f"Vol24h: {coin_data['volume_24h']}",
                f"Vol6h: {coin_data['volume_6h']}",
                f"Price: {coin_data['price']}",
                f"InitialBuy: {coin_data['initialBuy']}",
                f"SolAmount: {coin_data['solAmount']}",
                f"BondingCurve: {str(coin_data['bondingCurveKey'])[:12]}...",
                f"Trader: {str(coin_data['traderPublicKey'])[:12]}...",
                f"Pool: {coin_data['pool']}",
                f"TxType: {coin_data['txType']}",
                f"VTokens: {coin_data['vTokensInBondingCurve']}",
                f"VSol: {coin_data['vSolInBondingCurve']}",
                f"Name: \"{name}\"",
                f"URI: {str(coin_data['uri'])[:50]}..."
            ])

            # Log to all coins file
            all_coins_entry = f"[{timestamp}] [{'CATEGORIZED' if category else 'UNFILTERED'}] {symbol} ({mint[:12]}...) from {source} | {comprehensive_data}\n"
            with open(self.all_coins_file, 'a', encoding='utf-8') as f:
                f.write(all_coins_entry)
            self.stats["coins_logged"] += 1

            # Log to categorized coins file if it has a category
            if category:
                categorized_entry = f"[{timestamp}] [{category}] {symbol} ({mint[:12]}...) from {source} | {comprehensive_data}\n"
                with open(self.categorized_coins_file, 'a', encoding='utf-8') as f:
                    f.write(categorized_entry)
                self.stats["categorized_coins_logged"] += 1

                logger.info(f"📝 Logged categorized coin: {symbol} as {category} with comprehensive data")
            else:
                logger.info(f"📝 Logged unfiltered coin: {symbol} with comprehensive data")

            # Also log to JSON files if enabled
            if logging_config.enable_json_logging:
                self._log_coin_to_json_files(coin, category, coin_data, timestamp)

        except Exception as e:
            logger.error(f"❌ Failed to log coin to file: {e}")

    def _log_coin_to_json_files(self, coin: Dict[str, Any], category: Optional[str], coin_data: Dict[str, Any], timestamp: str):
        """Log coin data to JSON files"""
        try:
            # Create comprehensive coin object for JSON storage
            coin_json_object = {
                "timestamp": timestamp,
                "symbol": coin.get('symbol', 'Unknown'),
                "name": coin.get('name', 'Unknown'),
                "mint": coin.get('mint', 'Unknown'),
                "source": coin.get('source', 'Unknown'),
                "category": category or 'Unfiltered',
                "status": 'CATEGORIZED' if category else 'UNFILTERED',
                "metrics": {
                    "liquidity": coin_data['liquidity'],
                    "fdv": coin_data['fdv'],
                    "marketCap": coin_data['marketCap'],
                    "age": coin_data['age'],
                    "transactions1h": coin_data['txns_1h'],
                    "volume24h": coin_data['volume_24h'],
                    "volume6h": coin_data['volume_6h'],
                    "price": coin_data['price'],
                    "initialBuy": coin_data['initialBuy'],
                    "solAmount": coin_data['solAmount']
                },
                "blockchain": {
                    "bondingCurveKey": coin_data['bondingCurveKey'],
                    "traderPublicKey": coin_data['traderPublicKey'],
                    "pool": coin_data['pool'],
                    "txType": coin_data['txType'],
                    "vTokensInBondingCurve": coin_data['vTokensInBondingCurve'],
                    "vSolInBondingCurve": coin_data['vSolInBondingCurve'],
                    "signature": coin_data['signature']
                },
                "metadata": {
                    "uri": coin_data['uri'],
                    "discoveryTimestamp": coin_data['discoveryTimestamp'],
                    "filterTimestamp": coin_data['filterTimestamp']
                }
            }

            # Add to all coins JSON
            self._append_to_json_file(self.all_coins_json_file, coin_json_object)

            # Add to categorized coins JSON if it has a category
            if category:
                self._append_to_json_file(self.categorized_coins_json_file, coin_json_object)

        except Exception as e:
            logger.error(f"❌ Failed to log coin to JSON files: {e}")

    def _append_to_json_file(self, file_path: Path, coin_object: Dict[str, Any]):
        """Append coin data to JSON file"""
        try:
            # Read existing JSON file
            with open(file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)

            # Add new coin to the coins array
            json_data['coins'].append(coin_object)

            # Update metadata
            json_data['metadata']['lastUpdated'] = datetime.now().isoformat()
            json_data['metadata']['totalCoins'] = len(json_data['coins'])

            # Write back to file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.error(f"❌ Failed to append to JSON file {file_path}: {e}")

    async def _update_position(self, mint: str, trade_data: Dict[str, Any]):
        """Update position data for a monitored token"""
        try:
            if mint in self.position_cache:
                old_data = self.position_cache[mint].copy()

                # Update with new trade data
                self.position_cache[mint].update({
                    'marketCapSol': trade_data.get('marketCapSol', old_data.get('marketCapSol', 0)),
                    'lastUpdate': time.time(),
                    'lastTrade': trade_data
                })

                # Call position update callback
                if self.on_position_update:
                    await self.on_position_update(self.position_cache[mint], old_data)

        except Exception as e:
            logger.error(f"❌ Error updating position for {mint}: {e}")

    def add_position_monitoring(self, mint: str, initial_data: Dict[str, Any]):
        """Add a token to position monitoring"""
        self.position_cache[mint] = initial_data.copy()
        self.stats["positions_monitored"] += 1
        logger.info(f"📊 Added position monitoring for {mint[:12]}...")

    def remove_position_monitoring(self, mint: str):
        """Remove a token from position monitoring"""
        if mint in self.position_cache:
            del self.position_cache[mint]
            self.stats["positions_monitored"] -= 1
            logger.info(f"📊 Removed position monitoring for {mint[:12]}...")

    async def unsubscribe_token_trade(self, token: str):
        """Unsubscribe from token trade events"""
        try:
            if self.ws and token in self.active_subscriptions["token_trades"]:
                message = {"method": "unsubscribeTokenTrade", "keys": [token]}
                await self.ws.send(json.dumps(message))
                self.active_subscriptions["token_trades"].discard(token)
                logger.info(f"🚫 Unsubscribed from token trades: {token[:12]}...")
        except Exception as e:
            logger.error(f"❌ Error unsubscribing from token trade: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """Get current scraper statistics"""
        return self.stats.copy()
