"""
PumpPortal API integration for trading operations
"""

import asyncio
import json
import time
from typing import Optional, Dict, Any

import httpx
from solders.transaction import VersionedTransaction

from trading_bot.config.settings import trading_config
from trading_bot.utils.logging import get_logger
from trading_bot.utils.solana_client import SolanaClient

logger = get_logger("pump_portal")


class PumpPortalAPI:
    """PumpPortal API client for trading operations"""
    
    def __init__(self, solana_client: SolanaClient):
        self.solana_client = solana_client
        self.base_url = "https://pumpportal.fun/api"
        self.last_api_call = 0
        
        # HTTP client with timeout settings
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(30.0),
            limits=httpx.Limits(max_keepalive_connections=5, max_connections=10)
        )
    
    async def _rate_limited_delay(self):
        """Apply rate limiting between API calls"""
        now = time.time() * 1000  # Convert to milliseconds
        time_since_last_call = now - self.last_api_call
        
        if time_since_last_call < trading_config.api_rate_limit:
            delay_time = trading_config.api_rate_limit - time_since_last_call
            logger.info(f"⏱️ Rate limiting: Waiting {delay_time:.0f}ms before next API call")
            await asyncio.sleep(delay_time / 1000)  # Convert back to seconds
        
        self.last_api_call = time.time() * 1000
    
    async def buy_token(
        self,
        mint: str,
        amount: float,
        slippage: int = 10,
        priority_fee: Optional[float] = None
    ) -> Optional[str]:
        """
        Buy tokens using PumpPortal API
        
        Args:
            mint: Token mint address
            amount: Amount in SOL to spend
            slippage: Slippage tolerance percentage
            priority_fee: Priority fee (uses default if None)
            
        Returns:
            Transaction signature if successful, None otherwise
        """
        url = f"{self.base_url}/trade-local"
        
        data = {
            "publicKey": str(self.solana_client.payer.public_key),
            "action": "buy",
            "mint": mint,
            "amount": amount,
            "denominatedInSol": "true",
            "slippage": slippage,
            "priorityFee": priority_fee or trading_config.priority_fee_base,
            "pool": "pump"
        }
        
        try:
            # Apply rate limiting
            await self._rate_limited_delay()
            
            logger.info(f"Executing buy transaction for {amount} SOL of token {mint}")
            logger.info(f"🔗 API URL: {url}")
            logger.info(f"📤 Request data: {json.dumps(data)}")
            
            response = await self.client.post(
                url,
                json=data,
                headers={"Content-Type": "application/json"}
            )
            
            logger.info(f"📥 API Response Status: {response.status_code}")
            
            if response.status_code == 200:
                # Get the serialized transaction
                transaction_data = response.content
                logger.info(f"📥 Received transaction data ({len(transaction_data)} bytes)")
                
                # Deserialize the transaction
                tx = VersionedTransaction.from_bytes(transaction_data)
                
                # Send transaction using Solana client
                signature = await self.solana_client.send_transaction(tx)
                
                if signature:
                    logger.success(f"✅ Buy transaction successful: {signature}")
                    return signature
                else:
                    logger.error("❌ Buy transaction failed during sending")
                    return None
            else:
                error_text = response.text
                logger.error(f"❌ Buy transaction failed: {response.status_code} {response.reason_phrase}")
                logger.error(f"🚨 Error Response: {error_text}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error executing buy transaction: {e}")
            return None
    
    async def sell_token(
        self,
        mint: str,
        amount: float,
        slippage: int = 10,
        priority_fee: Optional[float] = None
    ) -> Optional[str]:
        """
        Sell tokens using PumpPortal API
        
        Args:
            mint: Token mint address
            amount: Amount of tokens to sell
            slippage: Slippage tolerance percentage
            priority_fee: Priority fee (uses default if None)
            
        Returns:
            Transaction signature if successful, None otherwise
        """
        url = f"{self.base_url}/trade-local"
        
        data = {
            "publicKey": str(self.solana_client.payer.public_key),
            "action": "sell",
            "mint": mint,
            "amount": str(amount),
            "denominatedInSol": "false",  # Selling tokens, not SOL
            "slippage": slippage,
            "priorityFee": priority_fee or trading_config.priority_fee_base,
            "pool": "pump"
        }
        
        try:
            # Apply rate limiting
            await self._rate_limited_delay()
            
            logger.info(f"Executing sell transaction for {amount} tokens of {mint}")
            logger.info(f"🔗 API URL: {url}")
            logger.info(f"📤 Request data: {json.dumps(data)}")
            
            response = await self.client.post(
                url,
                json=data,
                headers={"Content-Type": "application/json"}
            )
            
            logger.info(f"📥 API Response Status: {response.status_code}")
            
            if response.status_code == 200:
                # Get the serialized transaction
                transaction_data = response.content
                logger.info(f"📥 Received transaction data ({len(transaction_data)} bytes)")
                
                # Deserialize the transaction
                tx = VersionedTransaction.from_bytes(transaction_data)
                
                # Send transaction using Solana client
                signature = await self.solana_client.send_transaction(tx)
                
                if signature:
                    logger.success(f"✅ Sell transaction successful: {signature}")
                    return signature
                else:
                    logger.error("❌ Sell transaction failed during sending")
                    return None
            else:
                error_text = response.text
                logger.error(f"❌ Sell transaction failed: {response.status_code} {response.reason_phrase}")
                logger.error(f"🚨 Error Response: {error_text}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error executing sell transaction: {e}")
            return None
    
    async def sell_all_tokens(self) -> Dict[str, Any]:
        """
        Sell all SPL tokens in the wallet
        
        Returns:
            Dictionary with results of sell operations
        """
        results = {
            "total_tokens": 0,
            "successful_sells": 0,
            "failed_sells": 0,
            "skipped_tokens": 0,
            "transactions": []
        }
        
        try:
            tokens = await self.solana_client.get_spl_tokens(use_cache=False)
            results["total_tokens"] = len(tokens)
            
            logger.info(f"🔍 Found {len(tokens)} SPL tokens to process")
            
            for token in tokens:
                mint = token["mint"]
                amount = token["amount"]
                
                logger.info(f"Token Mint: {mint}")
                logger.info(f"Human-readable Amount: {amount}")
                
                if amount >= 1:  # Only sell if amount is 1 or more
                    logger.info(f"Selling {amount} of token {mint}")
                    
                    # Try selling with retries
                    attempts = 5
                    tx_hash = None
                    
                    while attempts > 0:
                        tx_hash = await self.sell_token(mint, amount)
                        if tx_hash:
                            logger.success(f"Sold {amount} of token {mint} with transaction hash: {tx_hash}")
                            results["successful_sells"] += 1
                            results["transactions"].append({
                                "mint": mint,
                                "amount": amount,
                                "signature": tx_hash,
                                "status": "success"
                            })
                            break
                        else:
                            attempts -= 1
                            if attempts > 0:
                                logger.warning(f"Retrying sell transaction... Attempts left: {attempts}")
                                await asyncio.sleep(5)  # Wait 5 seconds before retrying
                    
                    if not tx_hash:
                        logger.error(f"Failed to sell token {mint} after multiple attempts")
                        results["failed_sells"] += 1
                        results["transactions"].append({
                            "mint": mint,
                            "amount": amount,
                            "signature": None,
                            "status": "failed"
                        })
                else:
                    logger.info(f"Skipping token {mint} as the amount is less than 1")
                    results["skipped_tokens"] += 1
                    results["transactions"].append({
                        "mint": mint,
                        "amount": amount,
                        "signature": None,
                        "status": "skipped"
                    })
            
            logger.info("📊 SELL ALL TOKENS SUMMARY:")
            logger.info(f"Total tokens: {results['total_tokens']}")
            logger.info(f"Successful sells: {results['successful_sells']}")
            logger.info(f"Failed sells: {results['failed_sells']}")
            logger.info(f"Skipped tokens: {results['skipped_tokens']}")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Error in sell_all_tokens: {e}")
            return results
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()
