#!/usr/bin/env python3
"""
WebSocket test script for PumpPortal API - Python version
Tests subscription and unsubscribe functionality
"""

import asyncio
import json
import signal
import sys
from datetime import datetime
from typing import Optional

import websockets
from websockets.exceptions import ConnectionClosed, WebSocketException

from trading_bot.utils.logging import get_logger

logger = get_logger("websocket_test")


class PumpPortalWebSocketTest:
    """Test class for PumpPortal WebSocket functionality"""
    
    def __init__(self, test_token: str = "Bwc4EBE65qXVzZ9ZiieBraj9GZL4Y2d7NN7B9pXENWR2"):
        self.test_token = test_token
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        self.messages_received = 0
        self.subscription_confirmed = False
        self.unsubscribe_executed = False
        self.running = True
        
        # WebSocket URL
        self.ws_url = "wss://pumpportal.fun/api/data"
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info("🛑 Stopping WebSocket test...")
        self.running = False
        self._print_final_results()
        sys.exit(0)
    
    def _print_final_results(self):
        """Print final test results"""
        logger.info("📊 FINAL UNSUBSCRIBE TEST RESULTS:")
        logger.info(f"📡 Total messages received: {self.messages_received}")
        logger.info(f"✅ Subscription confirmed: {'YES' if self.subscription_confirmed else 'NO'}")
        logger.info(f"🚫 Unsubscribe executed: {'YES' if self.unsubscribe_executed else 'NO'}")
        logger.info(f"🎯 Test token: {self.test_token}")
        logger.info(f"🕐 Final time: {datetime.now().strftime('%H:%M:%S')}")
        
        if self.subscription_confirmed and self.unsubscribe_executed:
            logger.success("🎉 UNSUBSCRIBE FUNCTIONALITY TEST: SUCCESS!")
        else:
            logger.warning("⚠️ UNSUBSCRIBE FUNCTIONALITY TEST: INCOMPLETE")
    
    async def connect_and_test(self):
        """Main test function"""
        logger.info("🌐 Testing PumpPortal WebSocket with Unsubscribe Functionality!")
        logger.info(f"📡 Connecting to {self.ws_url}")
        logger.info("🎯 Testing: Subscribe → Wait 10 seconds → Unsubscribe")
        logger.info(f"🎯 Watching token: {self.test_token}")
        logger.info("📅 Timeline: Subscribe → 10s wait → Unsubscribe → 10s wait → End test")
        
        try:
            async with websockets.connect(self.ws_url) as websocket:
                self.websocket = websocket
                logger.success("✅ PumpPortal WebSocket connected successfully!")
                
                # Subscribe to token trades
                await self._subscribe_to_token()
                
                # Start message handling and status reporting tasks
                await asyncio.gather(
                    self._handle_messages(),
                    self._status_reporter(),
                    self._test_timeline()
                )
                
        except ConnectionClosed:
            logger.info("🔌 PumpPortal WebSocket disconnected")
        except WebSocketException as e:
            logger.error(f"❌ WebSocket error: {e}")
        except Exception as e:
            logger.error(f"❌ Unexpected error: {e}")
        finally:
            self._print_final_results()
    
    async def _subscribe_to_token(self):
        """Subscribe to token trades"""
        logger.info(f"📡 Subscribing to token trades for: {self.test_token[:12]}...")
        subscription_message = {
            "method": "subscribeTokenTrade",
            "keys": [self.test_token]
        }
        await self.websocket.send(json.dumps(subscription_message))
    
    async def _unsubscribe_from_token(self):
        """Unsubscribe from token trades"""
        logger.info(f"🚫 Unsubscribing from token trades for: {self.test_token[:12]}...")
        unsubscribe_message = {
            "method": "unsubscribeTokenTrade",
            "keys": [self.test_token]
        }
        await self.websocket.send(json.dumps(unsubscribe_message))
        self.unsubscribe_executed = True
        logger.info("⏰ Unsubscribe command sent after 10 seconds!")
    
    async def _handle_messages(self):
        """Handle incoming WebSocket messages"""
        try:
            async for message in self.websocket:
                if not self.running:
                    break
                
                try:
                    data = json.loads(message)
                    self.messages_received += 1
                    
                    logger.info(f"\n📨 Message #{self.messages_received} received:")
                    logger.info(f"🕐 Time: {datetime.now().strftime('%H:%M:%S')}")
                    
                    # Check for subscription confirmation
                    if data.get("message") and "Successfully subscribed" in data["message"]:
                        self.subscription_confirmed = True
                        logger.success(f"✅ SUBSCRIPTION CONFIRMED: {data['message']}")
                    
                    # Check for unsubscription confirmation
                    elif data.get("message") and "unsubscribed" in data["message"]:
                        logger.success(f"🚫 UNSUBSCRIPTION CONFIRMED: {data['message']}")
                    
                    # Handle trade data
                    elif data.get("signature") and data.get("mint"):
                        time_status = "(AFTER UNSUBSCRIBE)" if self.unsubscribe_executed else "(BEFORE UNSUBSCRIBE)"
                        logger.info(f"💰 TRADE DATA RECEIVED {time_status}:")
                        logger.info(f"🔗 Token: {data.get('mint', 'Unknown')[:12]}...")
                        logger.info(f"💵 Amount: {data.get('amount', 'Unknown')}")
                        logger.info(f"👤 Trader: {data.get('traderPublicKey', 'Unknown')[:12]}...")
                        logger.info(f"📊 Type: {data.get('txType', 'Unknown')}")
                        
                        if self.unsubscribe_executed:
                            logger.warning("⚠️ WARNING: Received trade data after unsubscribe! This should not happen.")
                    
                    # Handle other messages
                    else:
                        logger.info(f"📋 Other message: {data}")
                    
                    logger.info("---")
                    
                    # Status updates
                    if self.messages_received == 1:
                        logger.success("🎊 FIRST MESSAGE RECEIVED! WebSocket is working!")
                    elif self.messages_received == 5:
                        logger.success("🔥 5 messages received - Connection is stable!")
                
                except json.JSONDecodeError as e:
                    logger.error(f"❌ Error parsing message: {e}")
                    logger.info(f"Raw data: {message}")
                
        except ConnectionClosed:
            logger.info("🔌 WebSocket connection closed")
        except Exception as e:
            logger.error(f"❌ Error handling messages: {e}")
    
    async def _test_timeline(self):
        """Execute the test timeline"""
        # Wait 10 seconds then unsubscribe
        await asyncio.sleep(10)
        if self.running:
            await self._unsubscribe_from_token()
        
        # Wait another 10 seconds then close
        await asyncio.sleep(10)
        if self.running:
            logger.info("🔚 Test completed - closing connection")
            await self.websocket.close()
    
    async def _status_reporter(self):
        """Report status every 5 seconds"""
        while self.running:
            await asyncio.sleep(5)
            if self.running:
                logger.info(f"\n📊 TEST STATUS:")
                logger.info(f"📡 Messages received: {self.messages_received}")
                logger.info(f"✅ Subscription confirmed: {'YES' if self.subscription_confirmed else 'NO'}")
                logger.info(f"🚫 Unsubscribe executed: {'YES' if self.unsubscribe_executed else 'NO'}")
                logger.info(f"🕐 Time: {datetime.now().strftime('%H:%M:%S')}")
                logger.info("---")


async def main():
    """Main function to run the WebSocket test"""
    test = PumpPortalWebSocketTest()
    
    logger.info("⏳ Unsubscribe test running... Will auto-complete in 20 seconds or press Ctrl+C to stop")
    
    try:
        await test.connect_and_test()
    except KeyboardInterrupt:
        logger.info("🛑 Test interrupted by user")
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
