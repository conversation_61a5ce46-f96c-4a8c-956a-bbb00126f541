"""
Solana blockchain client utilities
"""

import json
import os
from typing import List, Dict, Any, Optional
from pathlib import Path

from solana.rpc.async_api import AsyncClient
from solana.rpc.commitment import Confirmed, Processed
from solana.keypair import Keypair
from solana.publickey import <PERSON>Key
from solders.transaction import VersionedTransaction
from spl.token.constants import TOKEN_PROGRAM_ID
from spl.token.instructions import get_associated_token_address

from trading_bot.config.settings import trading_config
from trading_bot.utils.logging import get_logger

logger = get_logger("solana_client")


class SolanaClient:
    """Solana blockchain client wrapper"""
    
    def __init__(self, rpc_url: Optional[str] = None, wallet_path: Optional[str] = None):
        self.rpc_url = rpc_url or trading_config.helius_rpc_url
        self.wallet_path = wallet_path or trading_config.solana_wallet_path
        
        # Initialize connection
        self.connection = AsyncClient(
            self.rpc_url,
            commitment=Confirmed,
            timeout=60.0
        )
        
        # Load wallet keypair
        self.payer = self._load_keypair()
        
        # Caching for rate limiting
        self.last_balance_check = 0
        self.last_spl_token_check = 0
        self.cached_balance = 0.0
        self.cached_tokens = []
        
        logger.info(f"💰 Wallet: {self.payer.public_key}")
    
    def _load_keypair(self) -> Keypair:
        """Load Solana wallet keypair from file"""
        try:
            wallet_path = Path(self.wallet_path)
            if not wallet_path.exists():
                raise FileNotFoundError(f"Wallet file not found: {self.wallet_path}")
            
            with open(wallet_path, 'r') as f:
                keypair_data = json.load(f)
            
            if isinstance(keypair_data, list):
                # Convert list to bytes
                private_key = bytes(keypair_data)
                keypair = Keypair.from_secret_key(private_key)
                logger.success("Private key loaded from keypair file")
                return keypair
            else:
                raise ValueError("Invalid keypair format")
                
        except Exception as e:
            logger.error(f"Error reading Solana wallet keypair: {e}")
            raise
    
    async def get_balance(self, use_cache: bool = True) -> float:
        """
        Get SOL balance for the wallet
        
        Args:
            use_cache: Whether to use cached balance to avoid rate limiting
            
        Returns:
            Balance in SOL
        """
        import time
        now = time.time() * 1000  # Convert to milliseconds
        
        # Only check balance every 2 minutes to avoid rate limiting
        if use_cache and now - self.last_balance_check < trading_config.balance_check_interval:
            logger.info(f"Current balance: {self.cached_balance} SOL (cached)")
            return self.cached_balance
        
        try:
            response = await self.connection.get_balance(self.payer.public_key)
            balance = response.value / 1e9  # Convert lamports to SOL
            
            self.cached_balance = balance
            self.last_balance_check = now
            
            logger.info(f"Current balance: {balance} SOL (fresh)")
            return balance
            
        except Exception as e:
            if "429" in str(e):
                logger.warning(f"Rate limited checking balance. Using cached value: {self.cached_balance} SOL")
                return self.cached_balance
            
            logger.error(f"Error checking balance: {e}. Using cached: {self.cached_balance} SOL")
            return self.cached_balance
    
    async def get_spl_tokens(self, use_cache: bool = True) -> List[Dict[str, Any]]:
        """
        Get SPL token balances for the wallet
        
        Args:
            use_cache: Whether to use cached tokens to avoid rate limiting
            
        Returns:
            List of token information dictionaries
        """
        import time
        now = time.time() * 1000  # Convert to milliseconds
        
        # Only check SPL tokens every 5 minutes to avoid rate limiting
        if use_cache and now - self.last_spl_token_check < trading_config.spl_token_check_interval:
            return self.cached_tokens
        
        try:
            response = await self.connection.get_token_accounts_by_owner(
                self.payer.public_key,
                {"programId": TOKEN_PROGRAM_ID}
            )
            
            tokens = []
            for account_info in response.value:
                try:
                    # Parse account data
                    account_data = account_info.account.data
                    
                    # Extract mint and amount from account data
                    # This is a simplified version - in production you'd use proper SPL token parsing
                    if len(account_data) >= 64:
                        # Mint is at bytes 0-32, amount is at bytes 64-72
                        mint_bytes = account_data[:32]
                        amount_bytes = account_data[64:72]
                        
                        mint = PublicKey(mint_bytes)
                        amount = int.from_bytes(amount_bytes, byteorder='little')
                        
                        # Convert to human readable (assuming 6 decimals for most tokens)
                        human_amount = amount / (10 ** 6)
                        
                        if human_amount > 1:  # Only include tokens with meaningful amounts
                            tokens.append({
                                "mint": str(mint),
                                "amount": human_amount,
                                "raw_amount": amount
                            })
                
                except Exception as e:
                    logger.debug(f"Error parsing token account: {e}")
                    continue
            
            self.cached_tokens = tokens
            self.last_spl_token_check = now
            
            logger.info(f"Found {len(tokens)} SPL tokens (fresh)")
            return tokens
            
        except Exception as e:
            if "429" in str(e):
                logger.warning("Rate limited fetching SPL tokens. Using cached data.")
                return self.cached_tokens
            
            logger.error(f"Error fetching SPL tokens: {e}. Using cached data.")
            return self.cached_tokens
    
    async def send_transaction(
        self,
        transaction: VersionedTransaction,
        max_retries: int = 2,
        skip_preflight: bool = False
    ) -> Optional[str]:
        """
        Send a transaction to the Solana network
        
        Args:
            transaction: The transaction to send
            max_retries: Maximum number of retries
            skip_preflight: Whether to skip preflight checks
            
        Returns:
            Transaction signature if successful, None otherwise
        """
        try:
            # Get fresh blockhash
            logger.info("🔄 Getting fresh blockhash...")
            blockhash_response = await self.connection.get_latest_blockhash(Confirmed)
            blockhash = blockhash_response.value.blockhash
            last_valid_block_height = blockhash_response.value.last_valid_block_height
            
            # Update transaction with fresh blockhash
            transaction.message.recent_blockhash = blockhash
            
            # Sign transaction
            transaction.sign([self.payer])
            
            # Send transaction
            logger.info("📤 Sending transaction with fresh blockhash and high priority...")
            response = await self.connection.send_transaction(
                transaction,
                opts={
                    "max_retries": max_retries,
                    "preflight_commitment": Processed,
                    "skip_preflight": skip_preflight
                }
            )
            
            signature = response.value
            
            # Wait for confirmation with timeout
            logger.info("⏳ Waiting for transaction confirmation (15s timeout)...")
            
            try:
                confirmation = await self.connection.confirm_transaction(
                    signature,
                    commitment=Processed,
                    sleep_seconds=1,
                    last_valid_block_height=last_valid_block_height
                )
                
                if confirmation.value[0].err:
                    logger.error(f"❌ Transaction failed: {confirmation.value[0].err}")
                    return None
                
                logger.success(f"✅ Transaction successful: {signature}")
                logger.info(f"🔗 Transaction: https://solscan.io/tx/{signature}")
                
                return signature
                
            except Exception as e:
                logger.error(f"❌ Transaction confirmation timeout or error: {e}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error sending transaction: {e}")
            return None
    
    async def calculate_rent_exemption(self, data_length: int) -> Optional[int]:
        """
        Calculate rent exemption amount for given data length
        
        Args:
            data_length: Length of account data in bytes
            
        Returns:
            Rent exemption amount in lamports
        """
        try:
            response = await self.connection.get_minimum_balance_for_rent_exemption(data_length)
            rent_exemption = response.value
            
            logger.info(f"Rent exemption amount for {data_length} bytes: {rent_exemption / 1e9} SOL")
            return rent_exemption
            
        except Exception as e:
            logger.error(f"Error calculating rent exemption: {e}")
            return None
    
    async def close(self):
        """Close the connection"""
        await self.connection.close()
