"""
Solana Trading Bot - Python Version

A secure Solana trading bot for pump.fun tokens with advanced features:
- GMGN.ai integration with stealth mode
- Adaptive trading system
- PumpPortal API integration
- Real-time WebSocket monitoring
- Comprehensive logging and UI
"""

__version__ = "1.0.0"
__author__ = "Secure Trading Bot"
__license__ = "MIT"

from .config.settings import TradingConfig, GMGNConfig, LoggingConfig

# Optional imports that require additional dependencies
try:
    from .utils.solana_client import SolanaClient
    from .utils.pump_portal import PumpPortalAPI
    from .gmgn_scraper import RevolutionaryGMGNScraper
    _optional_imports_available = True
except ImportError:
    SolanaClient = None
    PumpPortalAPI = None
    RevolutionaryGMGNScraper = None
    _optional_imports_available = False

__all__ = [
    "TradingConfig",
    "GMGNConfig",
    "LoggingConfig",
]

if _optional_imports_available:
    __all__.extend([
        "SolanaClient",
        "PumpPortalAPI",
        "RevolutionaryGMGNScraper",
    ])
