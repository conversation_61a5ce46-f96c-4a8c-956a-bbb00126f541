"""
Solana Trading Bot - Python Version

A secure Solana trading bot for pump.fun tokens with advanced features:
- GMGN.ai integration with stealth mode
- Adaptive trading system
- PumpPortal API integration
- Real-time WebSocket monitoring
- Comprehensive logging and UI
"""

__version__ = "1.0.0"
__author__ = "Secure Trading Bot"
__license__ = "MIT"

from .config.settings import TradingConfig, GMGNConfig, LoggingConfig
from .utils.solana_client import SolanaClient
from .utils.pump_portal import PumpPortalAPI
from .gmgn_scraper import RevolutionaryGMGNScraper

__all__ = [
    "TradingConfig",
    "GMGNConfig", 
    "LoggingConfig",
    "SolanaClient",
    "PumpPortalAPI",
    "RevolutionaryGMGNScraper",
]
