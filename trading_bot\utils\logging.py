"""
Enhanced logging utilities for the Solana Trading Bot
"""

import os
import sys
from pathlib import Path
from typing import Optional
from loguru import logger
from trading_bot.config.settings import logging_config


def setup_logging(
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    enable_file_logging: bool = True
) -> None:
    """
    Setup logging configuration for the trading bot
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        log_file: Optional log file path
        enable_file_logging: Whether to enable file logging
    """
    # Remove default logger
    logger.remove()
    
    # Console logging with colors
    logger.add(
        sys.stdout,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
               "<level>{message}</level>",
        colorize=True
    )
    
    # File logging if enabled
    if enable_file_logging:
        log_dir = Path(logging_config.log_directory)
        log_dir.mkdir(exist_ok=True)
        
        if log_file is None:
            log_file = log_dir / "trading-bot.log"
        
        logger.add(
            log_file,
            level=log_level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation="10 MB",
            retention="7 days",
            compression="zip"
        )


def get_logger(name: str = "trading_bot"):
    """
    Get a logger instance with the specified name
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return logger.bind(name=name)


class TradingBotLogger:
    """
    Custom logger class for trading bot with enhanced features
    """
    
    def __init__(self, name: str = "trading_bot"):
        self.logger = get_logger(name)
        self.log_file_path = None
        
        # Setup file logging if enabled
        if logging_config.enable_logging:
            self._setup_file_logging()
    
    def _setup_file_logging(self):
        """Setup file logging for trading events"""
        log_dir = Path(logging_config.log_directory)
        log_dir.mkdir(exist_ok=True)
        
        self.log_file_path = log_dir / "trading-bot.log"
    
    def log_to_file(self, message: str):
        """
        Log message to file
        
        Args:
            message: Message to log
        """
        if self.log_file_path:
            try:
                with open(self.log_file_path, 'a', encoding='utf-8') as f:
                    timestamp = logger._core.now().strftime("%Y-%m-%d %H:%M:%S")
                    f.write(f"[{timestamp}] {message}\n")
            except Exception as e:
                self.logger.error(f"Failed to write to log file: {e}")
    
    def update_log(self, message: str):
        """
        Log message to both console and file (equivalent to updateLog from Node.js)
        
        Args:
            message: Message to log
        """
        # Log to console
        self.logger.info(message)
        
        # Log to file
        self.log_to_file(message)
        
        # Log important events to console with special formatting
        if any(keyword in message for keyword in ['Buy', 'Sell', 'Error', 'Found']):
            self.logger.opt(colors=True).info(f"<yellow>{message}</yellow>")
    
    def info(self, message: str):
        """Log info message"""
        self.logger.info(message)
        self.log_to_file(message)
    
    def warning(self, message: str):
        """Log warning message"""
        self.logger.warning(message)
        self.log_to_file(message)
    
    def error(self, message: str):
        """Log error message"""
        self.logger.error(message)
        self.log_to_file(message)
    
    def debug(self, message: str):
        """Log debug message"""
        self.logger.debug(message)
        self.log_to_file(message)
    
    def success(self, message: str):
        """Log success message with special formatting"""
        self.logger.opt(colors=True).success(f"<green>✅ {message}</green>")
        self.log_to_file(f"✅ {message}")
    
    def trade_event(self, message: str):
        """Log trading event with special formatting"""
        self.logger.opt(colors=True).info(f"<cyan>💰 {message}</cyan>")
        self.log_to_file(f"💰 {message}")
    
    def market_event(self, message: str):
        """Log market event with special formatting"""
        self.logger.opt(colors=True).info(f"<magenta>📊 {message}</magenta>")
        self.log_to_file(f"📊 {message}")


# Global logger instance
trading_logger = TradingBotLogger()
